# 🚀 LM Studio Chat - Aplicación Mejorada

Una aplicación de chat avanzada para LM Studio con funcionalidades mejoradas de gestión de modelos, análisis de archivos y perfiles de personaje.

## ✨ Características Principales

### 🔧 Gestión de Modelos
- **Cargar/Expulsar modelos** directamente desde la interfaz
- **Detección automática** de modelos cargados
- **Indicadores visuales** de estado (cargado/disponible)
- **Soporte para modelos de visión** (LLaVA, Qwen2-VL, etc.) con indicador 👁️

### 👤 Sistema de Personajes Avanzado
- **Campo de mensaje del sistema editable** visible en la configuración
- **Carga automática** del prompt del personaje al seleccionarlo
- **Gestión completa** de perfiles de personaje
- **Prioridad del mensaje del sistema** sobre prompts de personaje

### 📁 Análisis Real de Archivos
- **Archivos de texto**: Análisis completo del contenido (.txt, .md, .csv, .json, etc.)
- **Imágenes**: Análisis técnico + soporte para modelos de visión
- **Documentos**: Análisis contextual de PDFs y documentos Word
- **Sin limitaciones artificiales** - la IA analiza el contenido real

### 🖼️ Análisis de Imágenes Mejorado
- **Detección automática** de modelos con capacidades de visión
- **Análisis técnico** (dimensiones, orientación, tamaño)
- **Inferencia inteligente** basada en nombres de archivo
- **Soporte completo** para modelos multimodales

## 🚀 Cómo Usar

### Instalación (Muy Simple)
1. **Descarga** el archivo `LM-Studio-Chat-Mejorado.html`
2. **Haz doble clic** en el archivo para abrirlo en tu navegador
3. ¡Listo! La aplicación funciona completamente offline

### Configuración Inicial
1. **Inicia LM Studio** en tu computadora
2. **Carga un modelo** en LM Studio
3. **Inicia el servidor local** (botón "Start Server" en LM Studio)
4. En la aplicación, haz clic en **"⚙️ Config"**
5. Haz clic en **"🔗 Probar Conexión"** para verificar la conexión
6. ¡Comienza a chatear!

### Funciones Avanzadas

#### Gestión de Modelos
- Ve a **Configuración** → selecciona un modelo → usa **"🚀 Cargar Modelo"** o **"🗑️ Expulsar Modelo"**
- Los modelos con soporte de visión muestran el icono 👁️

#### Mensaje del Sistema
- En **Configuración**, edita el campo **"Mensaje del Sistema"**
- Presiona **"✅ Aplicar Mensaje"** para activarlo
- Se actualiza automáticamente al seleccionar personajes

#### Análisis de Archivos
- **Arrastra y suelta** archivos en el área de chat
- **Soporta**: .txt, .md, .csv, .json, .xml, .html, .css, .js, .py, .pdf, .doc, .docx
- **Imágenes**: .jpg, .png, .gif, .bmp, .webp

## 🔧 Requisitos Técnicos

- **LM Studio** instalado y ejecutándose
- **Navegador web moderno** (Chrome, Firefox, Edge, Safari)
- **Conexión local** a LM Studio (por defecto: http://localhost:1234)

## 💡 Consejos de Uso

### Para Mejor Rendimiento
- Usa **modelos de visión** (LLaVA, Qwen2-VL) para análisis completo de imágenes
- **Archivos de texto pequeños** (< 1MB) funcionan mejor
- **Configura el mensaje del sistema** para obtener respuestas más específicas

### Solución de Problemas
- Si no conecta: Verifica que LM Studio esté ejecutándose y el servidor activo
- Si hay errores CORS: Algunos navegadores pueden bloquear archivos locales
- Si el modelo no carga: Asegúrate de que esté disponible en LM Studio

## 🆕 Novedades de Esta Versión

- ✅ **Funcionamiento offline completo** - solo necesitas hacer doble clic
- ✅ **Gestión visual de modelos** con indicadores de estado
- ✅ **Campo de mensaje del sistema editable** en la interfaz
- ✅ **Análisis real de contenido** sin limitaciones artificiales
- ✅ **Soporte mejorado para modelos de visión**
- ✅ **Interfaz más intuitiva** con mejor feedback visual

## 📝 Notas Importantes

- **Privacidad**: Todo funciona localmente, no se envían datos a servidores externos
- **Compatibilidad**: Funciona con cualquier modelo compatible con LM Studio
- **Actualizaciones**: Simplemente reemplaza el archivo HTML con nuevas versiones

---

**¡Disfruta de tu experiencia mejorada con LM Studio! 🎉**
