import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AppContext = createContext();

const initialState = {
  // Configuración del servidor
  serverUrl: 'http://192.168.1.100:1234',
  isConnected: false,
  models: [],
  currentLoadedModel: null,
  
  // Configuración de IA
  temperature: 0.7,
  maxTokens: 2048,
  systemMessage: '',
  
  // Personajes
  characters: [],
  currentCharacter: null,
  
  // Conversaciones
  conversations: [],
  currentConversation: null,
  
  // Archivos adjuntos
  attachedFiles: [],
  
  // Logs del sistema
  systemLogs: [],
  
  // Configuración de UI
  uiScale: 1.0,
  textScale: 1.0,
  
  // Estados de carga
  isLoading: false,
  isSending: false,
};

function appReducer(state, action) {
  switch (action.type) {
    case 'SET_SERVER_CONFIG':
      return {
        ...state,
        serverUrl: action.payload.serverUrl,
        isConnected: action.payload.isConnected,
      };
      
    case 'SET_MODELS':
      return {
        ...state,
        models: action.payload.models,
        currentLoadedModel: action.payload.currentLoadedModel,
      };
      
    case 'SET_AI_CONFIG':
      return {
        ...state,
        temperature: action.payload.temperature,
        maxTokens: action.payload.maxTokens,
        systemMessage: action.payload.systemMessage,
      };
      
    case 'SET_CHARACTER':
      return {
        ...state,
        currentCharacter: action.payload,
        systemMessage: action.payload ? action.payload.prompt : state.systemMessage,
      };
      
    case 'SET_CONVERSATIONS':
      return {
        ...state,
        conversations: action.payload,
      };
      
    case 'SET_CURRENT_CONVERSATION':
      return {
        ...state,
        currentConversation: action.payload,
      };
      
    case 'ADD_MESSAGE':
      if (!state.currentConversation) return state;
      
      const updatedConversation = {
        ...state.currentConversation,
        messages: [...state.currentConversation.messages, action.payload],
        updatedAt: new Date().toISOString(),
      };
      
      const updatedConversations = state.conversations.map(conv =>
        conv.id === updatedConversation.id ? updatedConversation : conv
      );
      
      return {
        ...state,
        currentConversation: updatedConversation,
        conversations: updatedConversations,
      };
      
    case 'SET_ATTACHED_FILES':
      return {
        ...state,
        attachedFiles: action.payload,
      };
      
    case 'ADD_SYSTEM_LOG':
      return {
        ...state,
        systemLogs: [action.payload, ...state.systemLogs.slice(0, 99)],
      };
      
    case 'SET_UI_SCALE':
      return {
        ...state,
        uiScale: action.payload,
      };
      
    case 'SET_TEXT_SCALE':
      return {
        ...state,
        textScale: action.payload,
      };
      
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
      
    case 'SET_SENDING':
      return {
        ...state,
        isSending: action.payload,
      };
      
    case 'LOAD_PERSISTED_DATA':
      return {
        ...state,
        ...action.payload,
      };
      
    default:
      return state;
  }
}

export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);
  
  // Cargar datos persistidos al iniciar
  useEffect(() => {
    loadPersistedData();
  }, []);
  
  // Guardar datos cuando cambien (con debounce)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      savePersistedData();
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [state.serverUrl, state.temperature, state.maxTokens, state.systemMessage,
      state.currentCharacter, state.conversations, state.uiScale, state.textScale]);
  
  const loadPersistedData = async () => {
    try {
      const savedData = await AsyncStorage.getItem('lmstudio_app_data');
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        dispatch({ type: 'LOAD_PERSISTED_DATA', payload: parsedData });
      }
    } catch (error) {
      console.error('Error loading persisted data:', error);
    }
  };
  
  const savePersistedData = async () => {
    try {
      const dataToSave = {
        serverUrl: state.serverUrl,
        temperature: state.temperature,
        maxTokens: state.maxTokens,
        systemMessage: state.systemMessage,
        currentCharacter: state.currentCharacter,
        conversations: state.conversations,
        uiScale: state.uiScale,
        textScale: state.textScale,
        systemLogs: state.systemLogs,
      };
      
      await AsyncStorage.setItem('lmstudio_app_data', JSON.stringify(dataToSave));
    } catch (error) {
      console.error('Error saving persisted data:', error);
    }
  };
  
  const addSystemLog = (type, message) => {
    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toLocaleTimeString(),
      type: type,
      message: message,
      fullTimestamp: new Date().toISOString(),
    };
    
    dispatch({ type: 'ADD_SYSTEM_LOG', payload: logEntry });
  };
  
  const value = {
    state,
    dispatch,
    addSystemLog,
  };
  
  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
