<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal</title>
    <style>
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }
        
        .modal.active {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            color: black;
        }
        
        .btn {
            padding: 10px 20px;
            background: blue;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Test de Modal</h1>
    <button class="btn" onclick="openModal('testModal')">Abrir Modal</button>
    
    <div class="modal" id="testModal">
        <div class="modal-content">
            <h2>Modal de <PERSON>rueba</h2>
            <p>Si ves esto, el modal funciona!</p>
            <button class="btn" onclick="closeModal('testModal')">Cerrar</button>
        </div>
    </div>
    
    <script>
        function openModal(modalId) {
            console.log('Abriendo modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                console.log('Modal abierto exitosamente');
            } else {
                console.error('Modal no encontrado:', modalId);
            }
        }
        
        function closeModal(modalId) {
            console.log('Cerrando modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                console.log('Modal cerrado exitosamente');
            } else {
                console.error('Modal no encontrado:', modalId);
            }
        }
        
        // Test al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Página cargada, JavaScript funcionando');
        });
    </script>
</body>
</html>
