import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Switch,
  Divider,
  Surface,
  ActivityIndicator,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import Slider from '@react-native-community/slider';

import { useApp } from '../context/AppContext';
import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';
import ModelSelector from '../components/ModelSelector';
import LMStudioService from '../services/LMStudioService';

export default function SettingsScreen() {
  const { state, dispatch, addSystemLog } = useApp();
  const [isConnecting, setIsConnecting] = useState(false);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  
  const {
    serverUrl,
    isConnected,
    models,
    currentLoadedModel,
    temperature,
    maxTokens,
    systemMessage,
    uiScale,
    textScale,
  } = state;

  const testConnection = async () => {
    setIsConnecting(true);
    try {
      const result = await LMStudioService.testConnection(serverUrl);
      
      dispatch({
        type: 'SET_SERVER_CONFIG',
        payload: {
          serverUrl,
          isConnected: result.success,
        },
      });
      
      if (result.success) {
        addSystemLog('success', 'Conectado a LM Studio exitosamente');
        loadModels();
      } else {
        addSystemLog('error', 'Error de conexión con LM Studio');
      }
    } catch (error) {
      addSystemLog('error', `Error de conexión: ${error.message}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const loadModels = async () => {
    setIsLoadingModels(true);
    try {
      const result = await LMStudioService.loadModels(serverUrl);
      
      dispatch({
        type: 'SET_MODELS',
        payload: {
          models: result.models,
          currentLoadedModel: result.currentLoadedModel,
        },
      });
      
      addSystemLog('success', `${result.models.length} modelos cargados`);
    } catch (error) {
      addSystemLog('error', `Error cargando modelos: ${error.message}`);
    } finally {
      setIsLoadingModels(false);
    }
  };

  const updateServerUrl = (url) => {
    dispatch({
      type: 'SET_SERVER_CONFIG',
      payload: {
        serverUrl: url,
        isConnected: false,
      },
    });
  };

  const updateAIConfig = (config) => {
    dispatch({
      type: 'SET_AI_CONFIG',
      payload: {
        temperature: config.temperature !== undefined ? config.temperature : temperature,
        maxTokens: config.maxTokens !== undefined ? config.maxTokens : maxTokens,
        systemMessage: config.systemMessage !== undefined ? config.systemMessage : systemMessage,
      },
    });
  };

  const updateUIScale = (scale) => {
    dispatch({ type: 'SET_UI_SCALE', payload: scale });
    addSystemLog('info', `Tamaño de interfaz: ${Math.round(scale * 100)}%`);
  };

  const updateTextScale = (scale) => {
    dispatch({ type: 'SET_TEXT_SCALE', payload: scale });
    addSystemLog('info', `Tamaño de texto: ${Math.round(scale * 100)}%`);
  };

  const clearSystemMessage = () => {
    updateAIConfig({ systemMessage: '' });
    addSystemLog('success', 'Mensaje del sistema limpiado');
  };

  const resetToDefaults = () => {
    Alert.alert(
      'Restablecer Configuración',
      '¿Estás seguro de que quieres restablecer toda la configuración a los valores por defecto?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Restablecer',
          style: 'destructive',
          onPress: () => {
            updateServerUrl('http://192.168.1.100:1234');
            updateAIConfig({
              temperature: 0.7,
              maxTokens: 2048,
              systemMessage: '',
            });
            updateUIScale(1.0);
            updateTextScale(1.0);
            addSystemLog('info', 'Configuración restablecida a valores por defecto');
          },
        },
      ]
    );
  };

  const styles = createStyles(uiScale, textScale);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        
        {/* Configuración del Servidor */}
        <Card style={styles.card}>
          <Card.Title title="🔗 Servidor LM Studio" titleStyle={styles.cardTitle} />
          <Card.Content>
            <TextInput
              label="URL del Servidor"
              value={serverUrl}
              onChangeText={updateServerUrl}
              placeholder="http://192.168.1.100:1234"
              style={styles.input}
              theme={{
                colors: {
                  primary: '#667eea',
                  onSurface: '#ffffff',
                  surface: 'rgba(255, 255, 255, 0.1)',
                }
              }}
            />
            
            <View style={styles.buttonRow}>
              <Button
                mode="contained"
                onPress={testConnection}
                disabled={isConnecting}
                style={[styles.button, { backgroundColor: isConnected ? '#28a745' : '#667eea' }]}
                contentStyle={styles.buttonContent}
              >
                {isConnecting ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  `${isConnected ? '✅' : '🔗'} ${isConnected ? 'Conectado' : 'Probar Conexión'}`
                )}
              </Button>
              
              <Button
                mode="outlined"
                onPress={loadModels}
                disabled={!isConnected || isLoadingModels}
                style={styles.button}
                contentStyle={styles.buttonContent}
              >
                {isLoadingModels ? (
                  <ActivityIndicator size="small" color="#667eea" />
                ) : (
                  '📥 Cargar Modelos'
                )}
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Selector de Modelo */}
        {models.length > 0 && (
          <ModelSelector
            models={models}
            currentModel={currentLoadedModel}
            onModelChange={(model) => dispatch({ type: 'SET_MODELS', payload: { models, currentLoadedModel: model } })}
            uiScale={uiScale}
            textScale={textScale}
          />
        )}

        {/* Configuración de IA */}
        <Card style={styles.card}>
          <Card.Title title="🤖 Configuración de IA" titleStyle={styles.cardTitle} />
          <Card.Content>
            {/* Temperatura */}
            <View style={styles.sliderContainer}>
              <Text style={styles.sliderLabel}>
                Temperatura: {temperature.toFixed(1)}
              </Text>
              <Slider
                style={styles.slider}
                minimumValue={0}
                maximumValue={2}
                value={temperature}
                onValueChange={(value) => updateAIConfig({ temperature: value })}
                step={0.1}
                minimumTrackTintColor="#667eea"
                maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
                thumbStyle={{ backgroundColor: '#667eea' }}
              />
              <Text style={styles.sliderDescription}>
                Controla la creatividad de las respuestas
              </Text>
            </View>

            <Divider style={styles.divider} />

            {/* Máximo de Tokens */}
            <View style={styles.sliderContainer}>
              <Text style={styles.sliderLabel}>
                Máximo de Tokens: {maxTokens}
              </Text>
              <Slider
                style={styles.slider}
                minimumValue={256}
                maximumValue={8192}
                value={maxTokens}
                onValueChange={(value) => updateAIConfig({ maxTokens: Math.round(value) })}
                step={256}
                minimumTrackTintColor="#667eea"
                maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
                thumbStyle={{ backgroundColor: '#667eea' }}
              />
              <Text style={styles.sliderDescription}>
                Longitud máxima de las respuestas
              </Text>
            </View>

            <Divider style={styles.divider} />

            {/* Mensaje del Sistema */}
            <TextInput
              label="Mensaje del Sistema"
              value={systemMessage}
              onChangeText={(text) => updateAIConfig({ systemMessage: text })}
              placeholder="Instrucciones para la IA..."
              multiline
              numberOfLines={4}
              style={styles.textArea}
              theme={{
                colors: {
                  primary: '#667eea',
                  onSurface: '#ffffff',
                  surface: 'rgba(255, 255, 255, 0.1)',
                }
              }}
            />
            
            <Button
              mode="outlined"
              onPress={clearSystemMessage}
              style={styles.clearButton}
              contentStyle={styles.buttonContent}
            >
              🗑️ Limpiar Mensaje
            </Button>
          </Card.Content>
        </Card>

        {/* Configuración de Interfaz */}
        <Card style={styles.card}>
          <Card.Title title="🎨 Configuración de Interfaz" titleStyle={styles.cardTitle} />
          <Card.Content>
            {/* Tamaño de Interfaz */}
            <View style={styles.sliderContainer}>
              <Text style={styles.sliderLabel}>
                Tamaño de Interfaz: {Math.round(uiScale * 100)}%
              </Text>
              <Slider
                style={styles.slider}
                minimumValue={0.7}
                maximumValue={1.5}
                value={uiScale}
                onValueChange={updateUIScale}
                step={0.1}
                minimumTrackTintColor="#667eea"
                maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
                thumbStyle={{ backgroundColor: '#667eea' }}
              />
              <Text style={styles.sliderDescription}>
                Ajusta el tamaño de botones y elementos
              </Text>
            </View>

            <Divider style={styles.divider} />

            {/* Tamaño de Texto */}
            <View style={styles.sliderContainer}>
              <Text style={styles.sliderLabel}>
                Tamaño de Texto: {Math.round(textScale * 100)}%
              </Text>
              <Slider
                style={styles.slider}
                minimumValue={0.8}
                maximumValue={1.8}
                value={textScale}
                onValueChange={updateTextScale}
                step={0.1}
                minimumTrackTintColor="#667eea"
                maximumTrackTintColor="rgba(255, 255, 255, 0.3)"
                thumbStyle={{ backgroundColor: '#667eea' }}
              />
              <Text style={styles.sliderDescription}>
                Ajusta el tamaño de todo el texto
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Botón de Reset */}
        <Button
          mode="outlined"
          onPress={resetToDefaults}
          style={styles.resetButton}
          contentStyle={styles.buttonContent}
          textColor="#ff6b6b"
        >
          🔄 Restablecer Configuración
        </Button>

      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (uiScale, textScale) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: getSpacing(16, uiScale),
    paddingBottom: getSpacing(32, uiScale),
  },
  card: {
    marginBottom: getSpacing(16, uiScale),
    backgroundColor: '#1a1a2e',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
  },
  cardTitle: {
    fontSize: getFontSize(16, textScale),
    color: '#ffffff',
    fontWeight: 'bold',
  },
  input: {
    marginBottom: getSpacing(16, uiScale),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    fontSize: getFontSize(16, textScale),
  },
  textArea: {
    marginBottom: getSpacing(16, uiScale),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    fontSize: getFontSize(14, textScale),
    minHeight: getScaledSize(100, uiScale),
  },
  buttonRow: {
    flexDirection: 'row',
    gap: getSpacing(8, uiScale),
    flexWrap: 'wrap',
  },
  button: {
    flex: 1,
    minWidth: getScaledSize(120, uiScale),
  },
  buttonContent: {
    height: getScaledSize(40, uiScale),
  },
  clearButton: {
    borderColor: '#ff6b6b',
  },
  resetButton: {
    borderColor: '#ff6b6b',
    marginTop: getSpacing(8, uiScale),
  },
  sliderContainer: {
    marginVertical: getSpacing(8, uiScale),
  },
  sliderLabel: {
    fontSize: getFontSize(16, textScale),
    color: '#ffffff',
    fontWeight: '500',
    marginBottom: getSpacing(8, uiScale),
  },
  slider: {
    width: '100%',
    height: getScaledSize(40, uiScale),
  },
  sliderDescription: {
    fontSize: getFontSize(12, textScale),
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: getSpacing(4, uiScale),
  },
  divider: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: getSpacing(16, uiScale),
  },
});
