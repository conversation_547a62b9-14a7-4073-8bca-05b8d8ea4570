import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  List,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useApp } from '../context/AppContext';
import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';
import ConversationManager from '../components/ConversationManager';
import CharacterManager from '../components/CharacterManager';
import SystemLogs from '../components/SystemLogs';

export default function OthersScreen() {
  const { state } = useApp();
  const [activeSection, setActiveSection] = useState(null);
  
  const { uiScale, textScale } = state;

  const sections = [
    {
      id: 'conversations',
      title: '💬 Gestión de Chats',
      description: 'Crear, editar y organizar conversaciones',
      icon: 'chat',
      component: ConversationManager,
    },
    {
      id: 'characters',
      title: '👤 Gestión de Personajes',
      description: 'Crear y seleccionar personalidades de IA',
      icon: 'person',
      component: CharacterManager,
    },
    {
      id: 'logs',
      title: '📋 Logs del Sistema',
      description: 'Ver mensajes técnicos y de debug',
      icon: 'list',
      component: SystemLogs,
    },
  ];

  const renderSection = (section) => {
    const Component = section.component;
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Button
            mode="text"
            onPress={() => setActiveSection(null)}
            style={styles.backButton}
            contentStyle={styles.backButtonContent}
          >
            ← Volver
          </Button>
        </View>
        <Component uiScale={uiScale} textScale={textScale} />
      </View>
    );
  };

  const styles = createStyles(uiScale, textScale);

  if (activeSection) {
    const section = sections.find(s => s.id === activeSection);
    return (
      <SafeAreaView style={styles.container}>
        {renderSection(section)}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        
        <Card style={styles.headerCard}>
          <Card.Content>
            <Text style={styles.headerTitle}>📋 Funciones Adicionales</Text>
            <Text style={styles.headerDescription}>
              Gestiona tus conversaciones, personajes y revisa los logs del sistema
            </Text>
          </Card.Content>
        </Card>

        {sections.map((section, index) => (
          <View key={section.id}>
            <Card style={styles.sectionCard}>
              <List.Item
                title={section.title}
                description={section.description}
                titleStyle={styles.sectionTitle}
                descriptionStyle={styles.sectionDescription}
                onPress={() => setActiveSection(section.id)}
                right={(props) => (
                  <List.Icon 
                    {...props} 
                    icon="chevron-right" 
                    color="rgba(255, 255, 255, 0.6)"
                  />
                )}
                style={styles.listItem}
              />
            </Card>
            
            {index < sections.length - 1 && <Divider style={styles.divider} />}
          </View>
        ))}

        {/* Información de la App */}
        <Card style={styles.infoCard}>
          <Card.Content>
            <Text style={styles.infoTitle}>ℹ️ Información</Text>
            <Text style={styles.infoText}>
              LM Studio Chat Mobile v1.0.0{'\n'}
              Desarrollado para Expo Go{'\n'}
              Compatible con modelos de visión
            </Text>
          </Card.Content>
        </Card>

      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (uiScale, textScale) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: getSpacing(16, uiScale),
    paddingBottom: getSpacing(32, uiScale),
  },
  headerCard: {
    marginBottom: getSpacing(24, uiScale),
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderColor: 'rgba(102, 126, 234, 0.3)',
    borderWidth: 1,
  },
  headerTitle: {
    fontSize: getFontSize(20, textScale),
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: getSpacing(8, uiScale),
  },
  headerDescription: {
    fontSize: getFontSize(14, textScale),
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: getFontSize(20, textScale),
  },
  sectionCard: {
    backgroundColor: '#1a1a2e',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    marginVertical: getSpacing(4, uiScale),
  },
  listItem: {
    paddingVertical: getSpacing(8, uiScale),
  },
  sectionTitle: {
    fontSize: getFontSize(16, textScale),
    color: '#ffffff',
    fontWeight: '600',
  },
  sectionDescription: {
    fontSize: getFontSize(13, textScale),
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: getSpacing(4, uiScale),
  },
  divider: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: getSpacing(8, uiScale),
  },
  infoCard: {
    marginTop: getSpacing(24, uiScale),
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
  },
  infoTitle: {
    fontSize: getFontSize(16, textScale),
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: getSpacing(8, uiScale),
  },
  infoText: {
    fontSize: getFontSize(13, textScale),
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: getFontSize(18, textScale),
  },
  sectionContainer: {
    flex: 1,
  },
  sectionHeader: {
    padding: getSpacing(16, uiScale),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  backButton: {
    alignSelf: 'flex-start',
  },
  backButtonContent: {
    height: getScaledSize(40, uiScale),
  },
});
