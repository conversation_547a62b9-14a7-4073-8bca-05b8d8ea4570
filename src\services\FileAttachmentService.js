import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

export default class FileAttachmentService {
  
  // Tipos de archivo soportados
  static supportedTypes = [
    'text/plain',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/markdown',
    'application/json',
    'text/csv',
    'application/xml',
    'text/xml',
    'text/html',
    'application/javascript',
    'text/javascript',
    'text/x-python',
    'text/x-c',
    'text/x-java-source',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/webp',
    'image/svg+xml'
  ];

  static supportedExtensions = [
    '.txt', '.pdf', '.doc', '.docx', '.md', '.json', '.csv', '.xml', 
    '.html', '.htm', '.js', '.py', '.cpp', '.java', '.c', '.h',
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'
  ];

  // Seleccionar archivos
  static async pickFiles() {
    try {
      // Mostrar opciones al usuario
      const options = await this.showFilePickerOptions();
      
      if (options === 'camera') {
        return await this.pickFromCamera();
      } else if (options === 'gallery') {
        return await this.pickFromGallery();
      } else if (options === 'documents') {
        return await this.pickDocuments();
      } else {
        return [];
      }
    } catch (error) {
      console.error('Error picking files:', error);
      throw new Error(`Error seleccionando archivos: ${error.message}`);
    }
  }

  // Mostrar opciones de selección
  static async showFilePickerOptions() {
    return new Promise((resolve) => {
      Alert.alert(
        'Seleccionar Archivos',
        'Elige el tipo de archivo que quieres adjuntar:',
        [
          {
            text: '📷 Cámara',
            onPress: () => resolve('camera'),
          },
          {
            text: '🖼️ Galería',
            onPress: () => resolve('gallery'),
          },
          {
            text: '📄 Documentos',
            onPress: () => resolve('documents'),
          },
          {
            text: 'Cancelar',
            style: 'cancel',
            onPress: () => resolve(null),
          },
        ]
      );
    });
  }

  // Tomar foto con cámara
  static async pickFromCamera() {
    try {
      // Solicitar permisos
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Se necesitan permisos de cámara');
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        return [{
          uri: asset.uri,
          name: `camera_${Date.now()}.jpg`,
          type: 'image/jpeg',
          size: asset.fileSize || 0,
        }];
      }

      return [];
    } catch (error) {
      throw new Error(`Error con la cámara: ${error.message}`);
    }
  }

  // Seleccionar de galería
  static async pickFromGallery() {
    try {
      // Solicitar permisos
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Se necesitan permisos de galería');
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        return result.assets.map(asset => ({
          uri: asset.uri,
          name: asset.fileName || `image_${Date.now()}.jpg`,
          type: asset.type || 'image/jpeg',
          size: asset.fileSize || 0,
        }));
      }

      return [];
    } catch (error) {
      throw new Error(`Error seleccionando imágenes: ${error.message}`);
    }
  }

  // Seleccionar documentos
  static async pickDocuments() {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        multiple: true,
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        return result.assets.map(asset => ({
          uri: asset.uri,
          name: asset.name,
          type: asset.mimeType,
          size: asset.size || 0,
        }));
      }

      return [];
    } catch (error) {
      throw new Error(`Error seleccionando documentos: ${error.message}`);
    }
  }

  // Verificar si el tipo de archivo es soportado
  static isFileTypeSupported(file) {
    return this.supportedTypes.includes(file.type) || 
           this.supportedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
  }

  // Procesar archivos seleccionados
  static async processFiles(files) {
    const processedFiles = [];

    for (const file of files) {
      if (!this.isFileTypeSupported(file)) {
        console.warn(`Tipo de archivo no soportado: ${file.name}`);
        continue;
      }

      try {
        const processedFile = await this.processFile(file);
        processedFiles.push(processedFile);
      } catch (error) {
        console.error(`Error procesando archivo ${file.name}:`, error);
      }
    }

    return processedFiles;
  }

  // Procesar un archivo individual
  static async processFile(file) {
    const fileId = Date.now() + Math.random().toString(36).substr(2, 9);
    const isImage = file.type.startsWith('image/');
    
    const fileObj = {
      id: fileId,
      name: file.name,
      size: file.size,
      type: file.type,
      isImage: isImage,
      content: null,
      status: 'reading',
      uri: file.uri,
    };

    try {
      if (isImage) {
        // Para imágenes, convertir a base64
        const base64 = await FileSystem.readAsStringAsync(file.uri, {
          encoding: FileSystem.EncodingType.Base64,
        });
        fileObj.content = `data:${file.type};base64,${base64}`;
      } else {
        // Para archivos de texto, leer como string
        const content = await FileSystem.readAsStringAsync(file.uri, {
          encoding: FileSystem.EncodingType.UTF8,
        });
        fileObj.content = content;
      }
      
      fileObj.status = 'ready';
    } catch (error) {
      console.error(`Error leyendo archivo ${file.name}:`, error);
      fileObj.status = 'error';
      fileObj.content = null;
    }

    return fileObj;
  }

  // Obtener icono para tipo de archivo
  static getFileIcon(filename) {
    const ext = filename.toLowerCase().split('.').pop();
    const icons = {
      'pdf': '📄',
      'txt': '📝',
      'doc': '📘',
      'docx': '📘',
      'md': '📋',
      'json': '📊',
      'csv': '📈',
      'xml': '🗂️',
      'html': '🌐',
      'js': '⚡',
      'py': '🐍',
      'java': '☕',
      'cpp': '⚙️',
      'c': '⚙️',
      'jpg': '🖼️',
      'jpeg': '🖼️',
      'png': '🖼️',
      'gif': '🎞️',
      'bmp': '🖼️',
      'webp': '🖼️',
      'svg': '🎨'
    };
    return icons[ext] || '📄';
  }

  // Formatear tamaño de archivo
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
