import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Dimensions,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Chip,
  Surface,
  ActivityIndicator,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { useApp } from '../context/AppContext';
import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';
import AttachedFiles from '../components/AttachedFiles';
import MessageBubble from '../components/MessageBubble';
import StatusBar from '../components/StatusBar';
import FileAttachmentService from '../services/FileAttachmentService';
import LMStudioService from '../services/LMStudioService';

const { width: screenWidth } = Dimensions.get('window');

export default function ChatScreen() {
  const { state, dispatch, addSystemLog } = useApp();
  const [message, setMessage] = useState('');
  const scrollViewRef = useRef(null);
  
  const {
    currentConversation,
    attachedFiles,
    isSending,
    uiScale,
    textScale,
    isConnected,
    currentLoadedModel,
  } = state;

  useEffect(() => {
    // Crear conversación inicial si no existe
    if (!currentConversation && state.conversations.length === 0) {
      createNewConversation();
    }
  }, []);

  const createNewConversation = () => {
    const newConversation = {
      id: Date.now().toString(),
      title: 'Chat Principal',
      messages: [],
      characterId: state.currentCharacter?.id || null,
      characterName: state.currentCharacter?.name || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    const updatedConversations = [newConversation, ...state.conversations];
    dispatch({ type: 'SET_CONVERSATIONS', payload: updatedConversations });
    dispatch({ type: 'SET_CURRENT_CONVERSATION', payload: newConversation });
  };

  const handleAttachFiles = async () => {
    try {
      const files = await FileAttachmentService.pickFiles();
      if (files.length > 0) {
        const processedFiles = await FileAttachmentService.processFiles(files);
        dispatch({ type: 'SET_ATTACHED_FILES', payload: [...attachedFiles, ...processedFiles] });
        addSystemLog('success', `${files.length} archivo(s) adjuntado(s)`);
      }
    } catch (error) {
      addSystemLog('error', `Error adjuntando archivos: ${error.message}`);
    }
  };

  const removeAttachedFile = (fileId) => {
    const updatedFiles = attachedFiles.filter(file => file.id !== fileId);
    dispatch({ type: 'SET_ATTACHED_FILES', payload: updatedFiles });
  };

  const sendMessage = async () => {
    if (!message.trim() && attachedFiles.length === 0) return;
    
    if (!isConnected) {
      addSystemLog('error', 'No hay conexión con LM Studio');
      return;
    }
    
    if (!currentLoadedModel) {
      addSystemLog('error', 'No hay modelo cargado');
      return;
    }

    try {
      dispatch({ type: 'SET_SENDING', payload: true });
      
      // Agregar mensaje del usuario
      const userMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: message,
        attachments: attachedFiles.length > 0 ? attachedFiles : null,
        timestamp: new Date().toISOString(),
      };
      
      dispatch({ type: 'ADD_MESSAGE', payload: userMessage });
      
      // Limpiar input y archivos adjuntos
      setMessage('');
      dispatch({ type: 'SET_ATTACHED_FILES', payload: [] });
      
      // Enviar a LM Studio
      const response = await LMStudioService.sendMessage(
        message,
        attachedFiles,
        state.systemMessage,
        state.temperature,
        state.maxTokens,
        currentLoadedModel,
        state.serverUrl
      );
      
      // Agregar respuesta de la IA
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        role: 'ai',
        content: response,
        timestamp: new Date().toISOString(),
      };
      
      dispatch({ type: 'ADD_MESSAGE', payload: aiMessage });
      addSystemLog('success', 'Mensaje enviado y respuesta recibida');
      
    } catch (error) {
      addSystemLog('error', `Error enviando mensaje: ${error.message}`);
    } finally {
      dispatch({ type: 'SET_SENDING', payload: false });
    }
  };

  const styles = createStyles(uiScale, textScale);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar />
      
      <KeyboardAvoidingView 
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {/* Chat Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
        >
          {!currentConversation?.messages?.length ? (
            <Card style={styles.welcomeCard}>
              <Card.Content>
                <Text style={styles.welcomeTitle}>🚀 ¡Bienvenido a LM Studio Chat!</Text>
                <Text style={styles.welcomeText}>
                  Conecta con LM Studio y comienza a chatear con IA.
                  {'\n\n'}📎 Adjunta imágenes y documentos
                  {'\n'}👤 Selecciona personajes
                  {'\n'}⚙️ Configura tu experiencia
                </Text>
              </Card.Content>
            </Card>
          ) : (
            currentConversation.messages.map((msg) => (
              <MessageBubble
                key={msg.id}
                message={msg}
                uiScale={uiScale}
                textScale={textScale}
              />
            ))
          )}
          
          {isSending && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#667eea" />
              <Text style={styles.loadingText}>Enviando...</Text>
            </View>
          )}
        </ScrollView>

        {/* Attached Files */}
        {attachedFiles.length > 0 && (
          <AttachedFiles
            files={attachedFiles}
            onRemove={removeAttachedFile}
            uiScale={uiScale}
            textScale={textScale}
          />
        )}

        {/* Input Area */}
        <Surface style={styles.inputContainer}>
          <View style={styles.inputRow}>
            <Button
              mode="contained-tonal"
              onPress={handleAttachFiles}
              style={styles.attachButton}
              contentStyle={styles.attachButtonContent}
            >
              📎
            </Button>
            
            <TextInput
              style={styles.textInput}
              value={message}
              onChangeText={setMessage}
              placeholder="Escribe tu mensaje..."
              placeholderTextColor="rgba(255, 255, 255, 0.6)"
              multiline
              maxLength={2000}
              theme={{
                colors: {
                  primary: '#667eea',
                  onSurface: '#ffffff',
                  surface: 'rgba(255, 255, 255, 0.1)',
                }
              }}
            />
            
            <Button
              mode="contained"
              onPress={sendMessage}
              disabled={(!message.trim() && attachedFiles.length === 0) || isSending}
              style={styles.sendButton}
              contentStyle={styles.sendButtonContent}
            >
              ➤
            </Button>
          </View>
        </Surface>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const createStyles = (uiScale, textScale) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  keyboardView: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: getSpacing(16, uiScale),
  },
  messagesContent: {
    paddingVertical: getSpacing(8, uiScale),
  },
  welcomeCard: {
    marginVertical: getSpacing(20, uiScale),
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderColor: 'rgba(102, 126, 234, 0.3)',
    borderWidth: 1,
  },
  welcomeTitle: {
    fontSize: getFontSize(18, textScale),
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: getSpacing(8, uiScale),
  },
  welcomeText: {
    fontSize: getFontSize(14, textScale),
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: getFontSize(20, textScale),
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: getSpacing(16, uiScale),
  },
  loadingText: {
    marginLeft: getSpacing(8, uiScale),
    fontSize: getFontSize(14, textScale),
    color: 'rgba(255, 255, 255, 0.7)',
  },
  inputContainer: {
    backgroundColor: '#1a1a2e',
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: getSpacing(16, uiScale),
    paddingVertical: getSpacing(12, uiScale),
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: getSpacing(8, uiScale),
  },
  attachButton: {
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    borderRadius: getScaledSize(8, uiScale),
  },
  attachButtonContent: {
    height: getScaledSize(40, uiScale),
    width: getScaledSize(40, uiScale),
  },
  textInput: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    fontSize: getFontSize(16, textScale),
    maxHeight: getScaledSize(120, uiScale),
  },
  sendButton: {
    backgroundColor: '#667eea',
    borderRadius: getScaledSize(8, uiScale),
  },
  sendButtonContent: {
    height: getScaledSize(40, uiScale),
    width: getScaledSize(40, uiScale),
  },
});
