#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Solucionando dependencias obsoletas...');
console.log('==========================================\n');

// Función para ejecutar comandos con manejo de errores
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completado\n`);
  } catch (error) {
    console.error(`❌ Error en ${description}:`, error.message);
    return false;
  }
  return true;
}

// Limpiar caché y dependencias
function cleanProject() {
  console.log('🧹 Limpiando proyecto...');
  
  // Limpiar caché de npm
  if (!runCommand('npm cache clean --force', 'Limpieza de caché npm')) {
    return false;
  }
  
  // Eliminar node_modules si existe
  if (fs.existsSync('node_modules')) {
    console.log('📂 Eliminando node_modules...');
    try {
      if (process.platform === 'win32') {
        execSync('rmdir /s /q node_modules', { stdio: 'inherit' });
      } else {
        execSync('rm -rf node_modules', { stdio: 'inherit' });
      }
      console.log('✅ node_modules eliminado\n');
    } catch (error) {
      console.error('❌ Error eliminando node_modules:', error.message);
      return false;
    }
  }
  
  // Eliminar package-lock.json si existe
  if (fs.existsSync('package-lock.json')) {
    console.log('📄 Eliminando package-lock.json...');
    fs.unlinkSync('package-lock.json');
    console.log('✅ package-lock.json eliminado\n');
  }
  
  return true;
}

// Instalar dependencias actualizadas
function installDependencies() {
  console.log('📦 Instalando dependencias actualizadas...');
  
  // Instalar con npm
  if (!runCommand('npm install', 'Instalación de dependencias')) {
    return false;
  }
  
  return true;
}

// Verificar instalación de Expo CLI
function checkExpoCLI() {
  console.log('📋 Verificando Expo CLI...');
  try {
    execSync('expo --version', { stdio: 'pipe' });
    console.log('✅ Expo CLI ya está instalado\n');
  } catch (error) {
    console.log('📥 Instalando Expo CLI...');
    if (!runCommand('npm install -g @expo/cli', 'Instalación de Expo CLI')) {
      return false;
    }
  }
  return true;
}

// Verificar configuración
function verifySetup() {
  console.log('🔍 Verificando configuración...');
  
  // Verificar que package.json tenga las dependencias correctas
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // Verificar dependencias críticas
    const criticalDeps = [
      'expo',
      'react',
      'react-native',
      '@react-native-community/cli'
    ];
    
    const missingDeps = criticalDeps.filter(dep => 
      !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
    );
    
    if (missingDeps.length > 0) {
      console.error('❌ Dependencias faltantes:', missingDeps);
      return false;
    }
    
    console.log('✅ Dependencias críticas verificadas');
    
    // Verificar que no haya dependencias obsoletas problemáticas
    const obsoleteDeps = ['react-native-async-storage', 'react-native-slider'];
    const foundObsolete = obsoleteDeps.filter(dep => 
      packageJson.dependencies[dep] || packageJson.devDependencies[dep]
    );
    
    if (foundObsolete.length > 0) {
      console.warn('⚠️ Dependencias obsoletas encontradas:', foundObsolete);
      console.log('💡 Estas han sido reemplazadas por versiones actualizadas');
    }
    
  } catch (error) {
    console.error('❌ Error leyendo package.json:', error.message);
    return false;
  }
  
  console.log('✅ Configuración verificada\n');
  return true;
}

// Ejecutar doctor de Expo
function runExpoDoctor() {
  console.log('🏥 Ejecutando Expo Doctor...');
  try {
    execSync('expo doctor', { stdio: 'inherit' });
    console.log('✅ Expo Doctor completado\n');
  } catch (error) {
    console.warn('⚠️ Expo Doctor encontró algunos problemas, pero el proyecto debería funcionar');
    console.log('💡 Revisa los warnings arriba si experimentas problemas\n');
  }
  return true;
}

// Mostrar resumen final
function showSummary() {
  console.log('🎉 ¡Dependencias solucionadas!');
  console.log('=============================\n');
  
  console.log('✅ Cambios realizados:');
  console.log('  • Actualizado a Expo SDK 50');
  console.log('  • Actualizado React Native a 0.73.6');
  console.log('  • Reemplazado react-native-async-storage por @react-native-async-storage/async-storage');
  console.log('  • Reemplazado react-native-slider por @react-native-community/slider');
  console.log('  • Agregado @react-native-community/cli');
  console.log('  • Actualizado todas las dependencias de Expo');
  console.log('  • Agregado permisos modernos para Android\n');
  
  console.log('📱 Próximos pasos:');
  console.log('  1. Ejecutar: npm start');
  console.log('  2. Escanear QR con Expo Go');
  console.log('  3. Configurar LM Studio en la app\n');
  
  console.log('🐛 Si encuentras problemas:');
  console.log('  • Reinicia Expo Go en tu móvil');
  console.log('  • Ejecuta: npm run start:clear');
  console.log('  • Verifica que LM Studio esté ejecutándose');
  console.log('  • Revisa INSTALACION.md para más detalles\n');
  
  console.log('🚀 ¡Listo para usar!');
}

// Función principal
function main() {
  try {
    // Limpiar proyecto
    if (!cleanProject()) {
      process.exit(1);
    }
    
    // Verificar/Instalar Expo CLI
    if (!checkExpoCLI()) {
      process.exit(1);
    }
    
    // Instalar dependencias
    if (!installDependencies()) {
      process.exit(1);
    }
    
    // Verificar configuración
    if (!verifySetup()) {
      process.exit(1);
    }
    
    // Ejecutar Expo Doctor
    runExpoDoctor();
    
    // Mostrar resumen
    showSummary();
    
  } catch (error) {
    console.error('\n❌ Error durante la corrección:', error.message);
    console.log('\n💡 Soluciones alternativas:');
    console.log('  1. Elimina manualmente node_modules y package-lock.json');
    console.log('  2. Ejecuta: npm cache clean --force');
    console.log('  3. Ejecuta: npm install');
    console.log('  4. Si persiste, usa: npm install --legacy-peer-deps');
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}

module.exports = { main, cleanProject, installDependencies, checkExpoCLI, verifySetup };
