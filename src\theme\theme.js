import { MD3DarkTheme } from 'react-native-paper';

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#667eea',
    primaryContainer: '#764ba2',
    secondary: '#51cf66',
    secondaryContainer: '#28a745',
    surface: '#1a1a2e',
    surfaceVariant: '#16213e',
    background: '#0f0f23',
    error: '#ff6b6b',
    errorContainer: '#dc3545',
    onPrimary: '#ffffff',
    onSecondary: '#ffffff',
    onSurface: '#ffffff',
    onBackground: '#ffffff',
    outline: 'rgba(255, 255, 255, 0.2)',
    outlineVariant: 'rgba(255, 255, 255, 0.1)',
  },
  dark: true,
};

export const getScaledSize = (baseSize, scale) => {
  return Math.round(baseSize * scale);
};

export const getFontSize = (baseSize, textScale) => {
  return Math.round(baseSize * textScale);
};

export const getSpacing = (baseSpacing, uiScale) => {
  return Math.round(baseSpacing * uiScale);
};
