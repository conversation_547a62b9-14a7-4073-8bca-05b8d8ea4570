# 📱 Guía de Instalación - LM Studio Chat Mobile

## 🚀 Instalación Rápida

### 1. Preparar el Entorno

#### Instalar Node.js
```bash
# Descargar desde https://nodejs.org/
# Versión recomendada: 18.x o superior
node --version  # Verificar instalación
npm --version   # Verificar npm
```

#### Instalar Expo CLI
```bash
# Instalar globalmente
npm install -g expo-cli

# Verificar instalación
expo --version
```

#### Instalar Expo Go en tu móvil
- **Android:** [Google Play Store](https://play.google.com/store/apps/details?id=host.exp.exponent)
- **iOS:** [App Store](https://apps.apple.com/app/expo-go/id982107779)

### 2. Configurar el Proyecto

#### Clonar y Configurar
```bash
# Navegar al directorio del proyecto
cd lm-studio-chat-mobile

# Instalar dependencias
npm install

# Verificar que todo esté correcto
expo doctor
```

#### Iniciar el Servidor de Desarrollo
```bash
# Iniciar Expo
expo start

# O con opciones específicas
expo start --clear    # Limpiar caché
expo start --tunnel   # Usar túnel (si hay problemas de red)
```

### 3. Conectar tu Dispositivo

#### Método 1: Escanear QR (Recomendado)
1. **Abrir Expo Go** en tu móvil
2. **Escanear el código QR** que aparece en la terminal/navegador
3. **Esperar** a que se cargue la aplicación

#### Método 2: Enviar Link
1. **Presionar 's'** en la terminal para enviar por email/SMS
2. **Abrir el link** en tu móvil
3. **Seleccionar** "Abrir con Expo Go"

#### Método 3: Misma Red WiFi
1. **Asegurar** que PC y móvil estén en la misma red
2. **Presionar 'w'** para abrir en navegador web
3. **Usar la URL** que aparece en el navegador

## 🔧 Configuración de LM Studio

### 1. Preparar LM Studio en tu PC

#### Descargar e Instalar
```bash
# Descargar desde: https://lmstudio.ai/
# Instalar siguiendo las instrucciones
```

#### Cargar un Modelo
1. **Abrir LM Studio**
2. **Ir a "Models"** en la barra lateral
3. **Buscar modelo** (recomendado: "gemma-2-2b-it" o "llava")
4. **Descargar** el modelo seleccionado
5. **Cargar** el modelo en la pestaña "Chat"

#### Iniciar el Servidor
1. **Ir a "Local Server"** en LM Studio
2. **Configurar puerto** (por defecto: 1234)
3. **Hacer clic en "Start Server"**
4. **Verificar** que aparezca "Server running on port 1234"

### 2. Obtener la IP de tu PC

#### Windows
```cmd
# Abrir CMD y ejecutar:
ipconfig

# Buscar "IPv4 Address" en tu adaptador de red WiFi
# Ejemplo: *************
```

#### macOS/Linux
```bash
# En terminal:
ifconfig

# O usar:
ip addr show

# Buscar la IP de tu interfaz WiFi
# Ejemplo: *************
```

#### Verificar Conectividad
```bash
# Desde otro dispositivo en la misma red:
curl http://*************:1234/v1/models

# Debe devolver una lista de modelos
```

## 📱 Configuración de la App

### 1. Primera Configuración

#### Conectar a LM Studio
1. **Abrir la app** en tu móvil
2. **Ir a "Configuración"** (pestaña inferior)
3. **Ingresar URL del servidor:** `http://*************:1234`
4. **Hacer clic en "Probar Conexión"**
5. **Verificar** que aparezca "✅ Conectado"

#### Cargar Modelos
1. **Hacer clic en "Cargar Modelos"**
2. **Esperar** a que se cargue la lista
3. **Seleccionar** un modelo de la lista
4. **Verificar** que aparezca en "Modelo Actual"

### 2. Ajustar Interfaz

#### Tamaño de Interfaz
- **Ir a "Configuración"**
- **Ajustar slider "Tamaño de Interfaz"** (70% - 150%)
- **Los cambios se aplican** inmediatamente

#### Tamaño de Texto
- **Ajustar slider "Tamaño de Texto"** (80% - 180%)
- **Perfecto para** diferentes tamaños de pantalla

### 3. Configurar Personajes (Opcional)

#### Seleccionar Personaje Predefinido
1. **Ir a "Otros" → "Gestión de Personajes"**
2. **Seleccionar** un personaje (Asistente, Profesor, Creativo, Analista)
3. **El prompt se aplica** automáticamente

#### Crear Personaje Personalizado
1. **Hacer clic en "Crear Nuevo Personaje"**
2. **Ingresar nombre** y seleccionar emoji
3. **Escribir prompt** personalizado
4. **Guardar** y seleccionar

## 🎯 Primer Uso

### 1. Chat Básico
1. **Ir a la pestaña "Chat"**
2. **Escribir mensaje:** "Hola, ¿cómo estás?"
3. **Hacer clic en "➤"** para enviar
4. **Esperar respuesta** de la IA

### 2. Adjuntar Archivos
1. **Hacer clic en "📎"** en el chat
2. **Seleccionar origen:**
   - 📷 Cámara
   - 🖼️ Galería  
   - 📄 Documentos
3. **Seleccionar archivo(s)**
4. **Escribir mensaje** sobre el archivo
5. **Enviar** para análisis

### 3. Análisis de Imágenes (Modelos con Visión)
1. **Cargar modelo con visión** (ej: LLaVA, Gemma con visión)
2. **Adjuntar imagen** desde galería o cámara
3. **Escribir:** "¿Qué ves en esta imagen?"
4. **Enviar** para análisis visual

## 🐛 Solución de Problemas

### Problemas de Conexión

#### "No se puede conectar a LM Studio"
```bash
# Verificar que LM Studio esté ejecutándose
# Verificar la IP de tu PC
# Verificar que ambos dispositivos estén en la misma WiFi
# Verificar firewall de Windows/macOS
```

#### "Error de red"
```bash
# Reiniciar LM Studio
# Reiniciar la app (cerrar y abrir Expo Go)
# Verificar que el puerto 1234 esté libre
# Probar con IP diferente si tienes múltiples adaptadores
```

### Problemas de la App

#### "App no carga"
```bash
# Verificar conexión a internet
# Reiniciar Expo Go
# Limpiar caché: expo start --clear
# Verificar que Node.js esté actualizado
```

#### "Archivos no se adjuntan"
```bash
# Verificar permisos de cámara/galería en el móvil
# Reiniciar la app
# Probar con archivos más pequeños
# Verificar formato de archivo soportado
```

### Problemas de Rendimiento

#### "App lenta"
```bash
# Reducir tamaño de interfaz y texto
# Cerrar otras apps en el móvil
# Verificar que el modelo no sea muy grande para tu PC
# Usar modelo más pequeño si es necesario
```

#### "Respuestas lentas"
```bash
# Verificar velocidad de WiFi
# Usar modelo más pequeño
# Verificar que LM Studio no esté sobrecargado
# Reducir max_tokens en configuración
```

## 📞 Soporte Adicional

### Logs del Sistema
1. **Ir a "Otros" → "Logs del Sistema"**
2. **Revisar errores** en rojo
3. **Filtrar por tipo** de log
4. **Exportar logs** para análisis

### Recursos Útiles
- **LM Studio Docs:** https://lmstudio.ai/docs
- **Expo Docs:** https://docs.expo.dev/
- **React Native Paper:** https://reactnativepaper.com/

### Comunidad
- **Discord de LM Studio**
- **GitHub Issues**
- **Stack Overflow** (tag: expo, react-native)

---

¡Listo! 🎉 Ahora tienes LM Studio Chat Mobile funcionando en tu dispositivo.
