import React from 'react';
import {
  View,
  StyleSheet,
  Image,
} from 'react-native';
import {
  Text,
  Card,
  IconButton,
  Surface,
} from 'react-native-paper';

import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';
import FileAttachmentService from '../services/FileAttachmentService';

export default function AttachedFiles({ files, onRemove, uiScale, textScale }) {
  if (!files || files.length === 0) {
    return null;
  }

  const styles = createStyles(uiScale, textScale);

  return (
    <Surface style={styles.container}>
      <Text style={styles.title}>
        📎 {files.length} archivo(s) adjunto(s)
      </Text>
      
      {files.map((file) => (
        <Card key={file.id} style={styles.fileCard}>
          <View style={styles.fileContent}>
            {/* Preview o icono */}
            <View style={styles.previewContainer}>
              {file.isImage && file.content ? (
                <Image 
                  source={{ uri: file.content }} 
                  style={styles.imagePreview}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.iconContainer}>
                  <Text style={styles.fileIcon}>
                    {FileAttachmentService.getFileIcon(file.name)}
                  </Text>
                </View>
              )}
            </View>

            {/* Información del archivo */}
            <View style={styles.fileInfo}>
              <Text style={styles.fileName} numberOfLines={1}>
                {file.name}
              </Text>
              <Text style={styles.fileDetails}>
                {FileAttachmentService.formatFileSize(file.size)} • {getStatusText(file.status)}
              </Text>
            </View>

            {/* Botón eliminar */}
            <IconButton
              icon="close"
              size={getScaledSize(20, uiScale)}
              iconColor="#ff6b6b"
              onPress={() => onRemove(file.id)}
              style={styles.removeButton}
            />
          </View>
        </Card>
      ))}
    </Surface>
  );
}

const getStatusText = (status) => {
  switch (status) {
    case 'ready':
      return '✅ Listo';
    case 'reading':
      return '⏳ Leyendo...';
    case 'error':
      return '❌ Error';
    default:
      return '⏳ Procesando...';
  }
};

const createStyles = (uiScale, textScale) => StyleSheet.create({
  container: {
    backgroundColor: '#1a1a2e',
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: getSpacing(16, uiScale),
    paddingVertical: getSpacing(12, uiScale),
  },
  title: {
    fontSize: getFontSize(14, textScale),
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
    marginBottom: getSpacing(8, uiScale),
  },
  fileCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
    marginBottom: getSpacing(8, uiScale),
    elevation: 1,
  },
  fileContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: getSpacing(8, uiScale),
  },
  previewContainer: {
    marginRight: getSpacing(12, uiScale),
  },
  imagePreview: {
    width: getScaledSize(40, uiScale),
    height: getScaledSize(40, uiScale),
    borderRadius: getScaledSize(6, uiScale),
  },
  iconContainer: {
    width: getScaledSize(40, uiScale),
    height: getScaledSize(40, uiScale),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: getScaledSize(6, uiScale),
    justifyContent: 'center',
    alignItems: 'center',
  },
  fileIcon: {
    fontSize: getFontSize(20, textScale),
  },
  fileInfo: {
    flex: 1,
    marginRight: getSpacing(8, uiScale),
  },
  fileName: {
    fontSize: getFontSize(14, textScale),
    color: '#ffffff',
    fontWeight: '500',
    marginBottom: getSpacing(2, uiScale),
  },
  fileDetails: {
    fontSize: getFontSize(12, textScale),
    color: 'rgba(255, 255, 255, 0.7)',
  },
  removeButton: {
    margin: 0,
    width: getScaledSize(32, uiScale),
    height: getScaledSize(32, uiScale),
  },
});
