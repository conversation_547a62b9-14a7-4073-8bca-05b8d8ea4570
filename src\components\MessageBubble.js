import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Menu,
  IconButton,
} from 'react-native-paper';

import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';
import { useApp } from '../context/AppContext';

export default function MessageBubble({ message, uiScale, textScale }) {
  const { dispatch, addSystemLog } = useApp();
  const [menuVisible, setMenuVisible] = useState(false);
  
  const isUser = message.role === 'user';
  const isAI = message.role === 'ai';

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleRegenerateResponse = () => {
    setMenuVisible(false);
    Alert.alert(
      'Regenerar Respuesta',
      '¿Quieres que la IA genere una nueva respuesta?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Regenerar',
          onPress: () => {
            // TODO: Implementar regeneración
            addSystemLog('info', 'Función de regeneración en desarrollo');
          },
        },
      ]
    );
  };

  const handleContinueFromHere = () => {
    setMenuVisible(false);
    Alert.alert(
      'Continuar desde aquí',
      '¿Quieres crear una nueva conversación desde este punto?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Continuar',
          onPress: () => {
            // TODO: Implementar continuación
            addSystemLog('info', 'Función de continuación en desarrollo');
          },
        },
      ]
    );
  };

  const handleEditMessage = () => {
    setMenuVisible(false);
    Alert.alert(
      'Editar Mensaje',
      'La función de edición estará disponible en una próxima actualización.',
      [{ text: 'OK' }]
    );
  };

  const styles = createStyles(uiScale, textScale, isUser, isAI);

  return (
    <View style={styles.container}>
      <Card style={styles.bubble}>
        <Card.Content style={styles.content}>
          {/* Header con rol y tiempo */}
          <View style={styles.header}>
            <Text style={styles.roleText}>
              {isUser ? '👤 Tú' : '🤖 IA'}
            </Text>
            <View style={styles.headerRight}>
              <Text style={styles.timeText}>
                {formatTime(message.timestamp)}
              </Text>
              
              {/* Menú de acciones */}
              <Menu
                visible={menuVisible}
                onDismiss={() => setMenuVisible(false)}
                anchor={
                  <IconButton
                    icon="dots-vertical"
                    size={getScaledSize(16, uiScale)}
                    iconColor="rgba(255, 255, 255, 0.6)"
                    onPress={() => setMenuVisible(true)}
                    style={styles.menuButton}
                  />
                }
                contentStyle={styles.menuContent}
              >
                {isUser && (
                  <Menu.Item
                    onPress={handleEditMessage}
                    title="✏️ Editar"
                    titleStyle={styles.menuItemText}
                  />
                )}
                {isAI && (
                  <Menu.Item
                    onPress={handleRegenerateResponse}
                    title="🔄 Regenerar"
                    titleStyle={styles.menuItemText}
                  />
                )}
                <Menu.Item
                  onPress={handleContinueFromHere}
                  title="➡️ Continuar desde aquí"
                  titleStyle={styles.menuItemText}
                />
              </Menu>
            </View>
          </View>

          {/* Contenido del mensaje */}
          <Text style={styles.messageText}>
            {message.content}
          </Text>

          {/* Archivos adjuntos */}
          {message.attachments && message.attachments.length > 0 && (
            <View style={styles.attachmentsContainer}>
              <Text style={styles.attachmentsLabel}>
                📎 {message.attachments.length} archivo(s) adjunto(s)
              </Text>
              {message.attachments.map((file, index) => (
                <View key={index} style={styles.attachmentItem}>
                  <Text style={styles.attachmentName}>
                    {getFileIcon(file.name)} {file.name}
                  </Text>
                  <Text style={styles.attachmentSize}>
                    {formatFileSize(file.size)}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </Card.Content>
      </Card>
    </View>
  );
}

const getFileIcon = (filename) => {
  const ext = filename.toLowerCase().split('.').pop();
  const icons = {
    'pdf': '📄',
    'txt': '📝',
    'doc': '📘',
    'docx': '📘',
    'md': '📋',
    'json': '📊',
    'csv': '📈',
    'xml': '🗂️',
    'html': '🌐',
    'js': '⚡',
    'py': '🐍',
    'java': '☕',
    'cpp': '⚙️',
    'c': '⚙️',
    'jpg': '🖼️',
    'jpeg': '🖼️',
    'png': '🖼️',
    'gif': '🎞️',
    'bmp': '🖼️',
    'webp': '🖼️',
    'svg': '🎨'
  };
  return icons[ext] || '📄';
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const createStyles = (uiScale, textScale, isUser, isAI) => StyleSheet.create({
  container: {
    marginVertical: getSpacing(4, uiScale),
    alignItems: isUser ? 'flex-end' : 'flex-start',
  },
  bubble: {
    maxWidth: '85%',
    minWidth: '30%',
    backgroundColor: isUser 
      ? 'rgba(102, 126, 234, 0.2)' 
      : isAI 
        ? 'rgba(255, 255, 255, 0.1)'
        : 'rgba(255, 193, 7, 0.2)',
    borderColor: isUser 
      ? 'rgba(102, 126, 234, 0.4)' 
      : isAI 
        ? 'rgba(255, 255, 255, 0.2)'
        : 'rgba(255, 193, 7, 0.4)',
    borderWidth: 1,
    elevation: 2,
  },
  content: {
    paddingVertical: getSpacing(8, uiScale),
    paddingHorizontal: getSpacing(12, uiScale),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getSpacing(8, uiScale),
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleText: {
    fontSize: getFontSize(12, textScale),
    fontWeight: 'bold',
    color: isUser ? '#667eea' : isAI ? '#51cf66' : '#ffd43b',
  },
  timeText: {
    fontSize: getFontSize(10, textScale),
    color: 'rgba(255, 255, 255, 0.5)',
    marginRight: getSpacing(4, uiScale),
  },
  menuButton: {
    margin: 0,
    width: getScaledSize(24, uiScale),
    height: getScaledSize(24, uiScale),
  },
  menuContent: {
    backgroundColor: '#1a1a2e',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
  },
  menuItemText: {
    fontSize: getFontSize(14, textScale),
    color: '#ffffff',
  },
  messageText: {
    fontSize: getFontSize(15, textScale),
    color: '#ffffff',
    lineHeight: getFontSize(22, textScale),
  },
  attachmentsContainer: {
    marginTop: getSpacing(12, uiScale),
    padding: getSpacing(8, uiScale),
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: getScaledSize(6, uiScale),
  },
  attachmentsLabel: {
    fontSize: getFontSize(12, textScale),
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
    marginBottom: getSpacing(6, uiScale),
  },
  attachmentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: getSpacing(2, uiScale),
  },
  attachmentName: {
    fontSize: getFontSize(11, textScale),
    color: 'rgba(255, 255, 255, 0.9)',
    flex: 1,
  },
  attachmentSize: {
    fontSize: getFontSize(10, textScale),
    color: 'rgba(255, 255, 255, 0.6)',
  },
});
