<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LM Studio Chat - Mejorado</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/6.1.3/ionicons.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #1a1a2e;
            color: white;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 1.2em;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease;
        }

        .message.user {
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-left: auto;
            color: white;
        }

        .message.ai {
            background: rgba(255, 255, 255, 0.1);
            margin-right: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message.system {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            text-align: center;
            margin: 10px auto;
            max-width: 90%;
            font-size: 0.9em;
        }

        .input-container {
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            padding: 12px 50px 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            resize: none;
            min-height: 48px;
            max-height: 120px;
            outline: none;
            backdrop-filter: blur(10px);
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .attach-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            font-size: 20px;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .attach-btn:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .send-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.1);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: #1a1a2e;
            padding: 25px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.3em;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .conversation-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
        }

        .conversation-item {
            padding: 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .conversation-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .conversation-item.active {
            background: rgba(102, 126, 234, 0.3);
        }

        .conversation-info {
            flex: 1;
        }

        .conversation-title {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .conversation-preview {
            font-size: 0.9em;
            color: rgba(255, 255, 255, 0.7);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-actions {
            display: flex;
            gap: 8px;
        }

        .icon-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .icon-btn:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .file-upload {
            position: relative;
            display: inline-block;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .file-item img {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            display: inline-block;
            margin-left: 8px;
        }

        .status-indicator.offline {
            background: #dc3545;
        }

        .character-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .character-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .character-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .character-card.active {
            border-color: #667eea;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
        }

        .character-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .character-name {
            font-size: 0.9em;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .character-desc {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.3;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 18px;
            margin-right: auto;
            max-width: 80px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .typing-indicator.show {
            display: block;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.7);
            animation: typing 1.4s infinite;
        }

        .dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.7;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-message {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.5);
            color: #ff6b6b;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        .success-message {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
            color: #51cf66;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.1em;
            }
            
            .header-actions {
                gap: 5px;
            }
            
            .btn {
                padding: 6px 12px;
                font-size: 13px;
            }
            
            .modal-content {
                width: 95%;
                padding: 20px;
            }
            
            .character-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>LM Studio Chat <span class="status-indicator" id="statusIndicator"></span></h1>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="openSettings()">
                    ⚙️ Config
                </button>
                <button class="btn btn-secondary" onclick="openConversations()">
                    💬 Chats
                </button>
                <button class="btn btn-secondary" onclick="openCharacters()">
                    👤 Personajes
                </button>
            </div>
        </div>

        <div class="main-content">
            <div class="chat-container" id="chatContainer">
                <div class="message system">
                    ¡Bienvenido a LM Studio Chat! Configura tu servidor y comienza a chatear.
                </div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                <div class="typing-dots">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
            </div>

            <div class="input-container">
                <div class="input-group">
                    <div class="input-wrapper">
                        <textarea 
                            class="message-input" 
                            id="messageInput" 
                            placeholder="Escribe tu mensaje..."
                            rows="1"
                            onkeydown="handleKeyPress(event)"
                            oninput="autoResize(this)"
                        ></textarea>
                        <button class="attach-btn" onclick="document.getElementById('fileInput').click()">
                            📎
                        </button>
                        <input type="file" id="fileInput" style="display:none" multiple accept="image/*,.pdf,.txt,.doc,.docx,.md,.csv,.json,.xml,.html,.css,.js,.py,.java,.cpp,.c,.h" onchange="handleFileSelect(event)">
                    </div>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                        ➤
                    </button>
                </div>
                <div class="file-preview" id="filePreview"></div>
            </div>
        </div>
    </div>

    <!-- Modal de Configuración -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Configuración</h2>
                <button class="close-btn" onclick="closeModal('settingsModal')">&times;</button>
            </div>
            <div class="form-group">
                <label class="form-label">Servidor LM Studio</label>
                <input type="text" class="form-input" id="serverUrl" placeholder="http://*************:1234" value="http://localhost:1234">
            </div>
            <div class="form-group">
                <label class="form-label">Modelo Actual</label>
                <select class="form-select" id="modelSelect">
                    <option value="">Seleccionar modelo...</option>
                </select>
                <div style="margin-top: 10px; display: flex; gap: 10px;">
                    <button class="btn btn-secondary" onclick="loadSelectedModel()" id="loadModelBtn" disabled>
                        🚀 Cargar Modelo
                    </button>
                    <button class="btn btn-secondary" onclick="unloadCurrentModel()" id="unloadModelBtn" disabled>
                        🗑️ Expulsar Modelo
                    </button>
                </div>
                <div id="modelStatus" style="margin-top: 10px; font-size: 0.9em; color: rgba(255, 255, 255, 0.8);"></div>
            </div>
            <div class="form-group">
                <label class="form-label">Mensaje del Sistema</label>
                <textarea class="form-textarea" id="systemMessage" placeholder="Eres un asistente útil y amigable..." style="min-height: 80px;"></textarea>
                <div style="margin-top: 10px; display: flex; gap: 10px;">
                    <button class="btn btn-secondary" onclick="applySystemMessage()">
                        ✅ Aplicar Mensaje
                    </button>
                    <button class="btn btn-secondary" onclick="clearSystemMessage()">
                        🗑️ Limpiar
                    </button>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Temperatura</label>
                <input type="range" class="form-input" id="temperature" min="0" max="1" step="0.1" value="0.7">
                <span id="temperatureValue">0.7</span>
            </div>
            <div class="form-group">
                <label class="form-label">Máximo de Tokens</label>
                <input type="number" class="form-input" id="maxTokens" value="2048">
            </div>
            <div class="form-group">
                <button class="btn btn-primary" onclick="testConnection()">🔗 Probar Conexión</button>
                <button class="btn btn-primary" onclick="loadModels()">🔄 Actualizar Modelos</button>
            </div>
            <div id="connectionStatus"></div>
        </div>
    </div>

    <!-- Modal de Conversaciones -->
    <div class="modal" id="conversationsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Conversaciones</h2>
                <button class="close-btn" onclick="closeModal('conversationsModal')">&times;</button>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" onclick="newConversation()">+ Nueva Conversación</button>
            </div>
            <div class="conversation-list" id="conversationList">
                <!-- Las conversaciones se cargarán aquí -->
            </div>
        </div>
    </div>

    <!-- Modal de Personajes -->
    <div class="modal" id="charactersModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Personajes</h2>
                <button class="close-btn" onclick="closeModal('charactersModal')">&times;</button>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" onclick="createCharacter()">+ Crear Personaje</button>
            </div>
            <div class="character-grid" id="characterGrid">
                <!-- Los personajes se cargarán aquí -->
            </div>
        </div>
    </div>

    <!-- Modal de Crear/Editar Personaje -->
    <div class="modal" id="characterModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="characterModalTitle">Crear Personaje</h2>
                <button class="close-btn" onclick="closeModal('characterModal')">&times;</button>
            </div>
            <div class="form-group">
                <label class="form-label">Nombre</label>
                <input type="text" class="form-input" id="characterName" placeholder="Nombre del personaje">
            </div>
            <div class="form-group">
                <label class="form-label">Descripción</label>
                <input type="text" class="form-input" id="characterDesc" placeholder="Descripción breve">
            </div>
            <div class="form-group">
                <label class="form-label">Prompt del Sistema</label>
                <textarea class="form-textarea" id="characterPrompt" placeholder="Eres un asistente útil que..."></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">Emoji/Avatar</label>
                <input type="text" class="form-input" id="characterEmoji" placeholder="🤖" maxlength="2">
            </div>
            <div class="form-group">
                <button class="btn btn-primary" onclick="saveCharacter()">💾 Guardar</button>
                <button class="btn btn-secondary" onclick="closeModal('characterModal')">❌ Cancelar</button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let currentConversation = null;
        let conversations = [];
        let characters = [];
        let currentCharacter = null;
        let attachedFiles = [];
        let isConnected = false;
        let models = [];
        let currentLoadedModel = null;
        let currentSystemMessage = '';

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            loadFromStorage();
            initializeApp();
            updateConnectionStatus();
        });

        // Funciones de inicialización
        function initializeApp() {
            loadConversations();
            loadCharacters();
            loadModels();
            
            // Configurar eventos
            document.getElementById('temperature').addEventListener('input', function() {
                document.getElementById('temperatureValue').textContent = this.value;
            });

            document.getElementById('modelSelect').addEventListener('change', function() {
                updateModelButtons();
            });
        }

        function loadFromStorage() {
            const savedConversations = localStorage.getItem('lmstudio_conversations');
            if (savedConversations) {
                conversations = JSON.parse(savedConversations);
            }

            const savedCharacters = localStorage.getItem('lmstudio_characters');
            if (savedCharacters) {
                characters = JSON.parse(savedCharacters);
            } else {
                // Crear personajes por defecto
                characters = [
                    {
                        id: 'default',
                        name: 'Asistente',
                        description: 'Asistente general',
                        prompt: 'Eres un asistente útil y amigable.',
                        emoji: '🤖'
                    }
                ];
                saveToStorage();
            }

            const savedSettings = localStorage.getItem('lmstudio_settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                document.getElementById('serverUrl').value = settings.serverUrl || 'http://localhost:1234';
                document.getElementById('temperature').value = settings.temperature || 0.7;
                document.getElementById('maxTokens').value = settings.maxTokens || 2048;
                document.getElementById('temperatureValue').textContent = settings.temperature || 0.7;
                document.getElementById('systemMessage').value = settings.systemMessage || '';
                currentSystemMessage = settings.systemMessage || '';
            }
        }

        function saveToStorage() {
            localStorage.setItem('lmstudio_conversations', JSON.stringify(conversations));
            localStorage.setItem('lmstudio_characters', JSON.stringify(characters));
            
            const settings = {
                serverUrl: document.getElementById('serverUrl').value,
                temperature: parseFloat(document.getElementById('temperature').value),
                maxTokens: parseInt(document.getElementById('maxTokens').value),
                systemMessage: document.getElementById('systemMessage').value
            };
            localStorage.setItem('lmstudio_settings', JSON.stringify(settings));
        }

        // Funciones de conexión
        async function testConnection() {
            const serverUrl = document.getElementById('serverUrl').value;
            const statusDiv = document.getElementById('connectionStatus');
            
            try {
                statusDiv.innerHTML = '<div class="success-message">Probando conexión...</div>';
                
                const response = await fetch(`${serverUrl}/v1/models`);
                if (response.ok) {
                    isConnected = true;
                    statusDiv.innerHTML = '<div class="success-message">✅ Conexión exitosa!</div>';
                    updateConnectionStatus();
                    await loadModels();
                } else {
                    throw new Error('Error de conexión');
                }
            } catch (error) {
                isConnected = false;
                statusDiv.innerHTML = '<div class="error-message">❌ Error de conexión. Verifica la URL del servidor.</div>';
                updateConnectionStatus();
            }
        }

        async function loadModels() {
            const serverUrl = document.getElementById('serverUrl').value;
            const modelSelect = document.getElementById('modelSelect');

            try {
                // Intentar usar la nueva API de LM Studio primero
                let response = await fetch(`${serverUrl}/api/v0/models`);
                if (!response.ok) {
                    // Fallback a la API compatible con OpenAI
                    response = await fetch(`${serverUrl}/v1/models`);
                }

                if (response.ok) {
                    const data = await response.json();
                    models = data.data || [];

                    modelSelect.innerHTML = '<option value="">Seleccionar modelo...</option>';
                    models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;

                        // Mostrar estado del modelo si está disponible
                        let displayText = model.id;
                        if (model.state) {
                            displayText += ` (${model.state === 'loaded' ? '🟢 Cargado' : '⚪ Disponible'})`;
                        }

                        option.textContent = displayText;
                        modelSelect.appendChild(option);
                    });

                    // Detectar modelo actualmente cargado
                    const loadedModel = models.find(m => m.state === 'loaded');
                    if (loadedModel) {
                        currentLoadedModel = loadedModel.id;
                        modelSelect.value = loadedModel.id;
                    }

                    updateModelButtons();
                    updateModelStatus();
                }
            } catch (error) {
                console.error('Error cargando modelos:', error);
                showError('Error cargando lista de modelos');
            }
        }

        function updateConnectionStatus() {
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator ${isConnected ? '' : 'offline'}`;
        }

        // Funciones de gestión de modelos
        function updateModelButtons() {
            const selectedModel = document.getElementById('modelSelect').value;
            const loadBtn = document.getElementById('loadModelBtn');
            const unloadBtn = document.getElementById('unloadModelBtn');

            loadBtn.disabled = !selectedModel || selectedModel === currentLoadedModel;
            unloadBtn.disabled = !currentLoadedModel;
        }

        function updateModelStatus() {
            const statusDiv = document.getElementById('modelStatus');
            if (currentLoadedModel) {
                statusDiv.innerHTML = `<span style="color: #51cf66;">🟢 Modelo cargado: ${currentLoadedModel}</span>`;
            } else {
                statusDiv.innerHTML = `<span style="color: rgba(255, 255, 255, 0.6);">⚪ Ningún modelo cargado</span>`;
            }
        }

        async function loadSelectedModel() {
            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel) return;

            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.innerHTML = '<div class="success-message">Cargando modelo...</div>';

            try {
                // Simular carga de modelo haciendo una petición de prueba
                const serverUrl = document.getElementById('serverUrl').value;
                const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        messages: [{ role: 'user', content: 'test' }],
                        max_tokens: 1,
                        temperature: 0.1
                    })
                });

                if (response.ok) {
                    currentLoadedModel = selectedModel;
                    statusDiv.innerHTML = '<div class="success-message">✅ Modelo cargado exitosamente!</div>';
                    updateModelButtons();
                    updateModelStatus();
                    await loadModels(); // Refrescar lista
                } else {
                    throw new Error('Error cargando modelo');
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="error-message">❌ Error cargando modelo. Verifica que LM Studio esté ejecutándose.</div>';
                console.error('Error:', error);
            }
        }

        async function unloadCurrentModel() {
            if (!currentLoadedModel) return;

            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.innerHTML = '<div class="success-message">Expulsando modelo...</div>';

            try {
                // Nota: LM Studio no tiene endpoint directo para descargar modelos
                // Esta es una simulación - en la práctica el modelo se descarga automáticamente
                // cuando se carga otro o después de un tiempo de inactividad

                currentLoadedModel = null;
                statusDiv.innerHTML = '<div class="success-message">✅ Modelo expulsado (simulado)!</div>';
                updateModelButtons();
                updateModelStatus();

                // Limpiar selección
                document.getElementById('modelSelect').value = '';

                setTimeout(() => {
                    statusDiv.innerHTML = '<div style="color: rgba(255, 255, 255, 0.8);">💡 Nota: LM Studio gestiona automáticamente la memoria de modelos.</div>';
                }, 3000);

            } catch (error) {
                statusDiv.innerHTML = '<div class="error-message">❌ Error expulsando modelo.</div>';
                console.error('Error:', error);
            }
        }

        // Funciones de mensaje del sistema
        function applySystemMessage() {
            const systemMessage = document.getElementById('systemMessage').value.trim();
            currentSystemMessage = systemMessage;

            if (systemMessage) {
                addMessage('system', `Mensaje del sistema aplicado: ${systemMessage.substring(0, 100)}${systemMessage.length > 100 ? '...' : ''}`);
            } else {
                addMessage('system', 'Mensaje del sistema limpiado');
            }

            saveToStorage();
        }

        function clearSystemMessage() {
            document.getElementById('systemMessage').value = '';
            currentSystemMessage = '';
            addMessage('system', 'Mensaje del sistema limpiado');
            saveToStorage();
        }

        function loadCharacterPrompt(character) {
            if (character && character.prompt) {
                document.getElementById('systemMessage').value = character.prompt;
                currentSystemMessage = character.prompt;
                addMessage('system', `Prompt de ${character.name} cargado en mensaje del sistema`);
            }
        }

        // Funciones de chat
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message && attachedFiles.length === 0) return;
            
            if (!isConnected) {
                showError('No hay conexión con el servidor. Configura la conexión primero.');
                return;
            }

            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel) {
                showError('Selecciona un modelo primero.');
                return;
            }

            // Construir mensaje completo con archivos
            let fullMessage = message;
            let hasImages = false;
            
            // Procesar archivos adjuntos
            if (attachedFiles.length > 0) {
                let fileContent = '';
                
                for (const file of attachedFiles) {
                    if (file.type.startsWith('image/')) {
                        hasImages = true;
                        fileContent += `\n\n${file.processedContent}`;
                    } else {
                        fileContent += `\n\n${file.processedContent}`;
                    }
                }

                if (hasImages && !message) {
                    fullMessage = "He adjuntado una imagen. Por favor, ayúdame con el análisis basándote en la información técnica disponible y el nombre del archivo.";
                } else if (attachedFiles.length > 0 && !message) {
                    fullMessage = "He adjuntado archivos. Por favor, analiza el contenido disponible.";
                }
                
                fullMessage += fileContent;
            }

            // Agregar mensaje del usuario (solo el texto visible)
            if (message) {
                addMessage('user', message);
            }

            // Mostrar archivos adjuntos en el chat
            if (attachedFiles.length > 0) {
                attachedFiles.forEach(file => {
                    if (file.type.startsWith('image/')) {
                        addImageMessage('user', file);
                    } else {
                        addMessage('user', `📎 ${file.name}`);
                    }
                });
            }

            input.value = '';
            autoResize(input);
            clearAttachedFiles();
            
            // Mostrar indicador de escritura
            showTypingIndicator();
            
            try {
                const response = await callLMStudio(fullMessage, selectedModel);
                hideTypingIndicator();
                addMessage('ai', response);
                
                // Guardar en conversación actual
                if (currentConversation) {
                    saveCurrentConversation();
                }
                
            } catch (error) {
                hideTypingIndicator();
                showError('Error al enviar mensaje: ' + error.message);
            }
        }

        async function callLMStudio(message, model) {
            const serverUrl = document.getElementById('serverUrl').value;
            const temperature = parseFloat(document.getElementById('temperature').value);
            const maxTokens = parseInt(document.getElementById('maxTokens').value);
            
            const messages = [];

            // Agregar mensaje del sistema actual (prioridad sobre personaje)
            if (currentSystemMessage) {
                messages.push({
                    role: 'system',
                    content: currentSystemMessage
                });
            } else if (currentCharacter && currentCharacter.prompt) {
                // Fallback al prompt del personaje si no hay mensaje del sistema
                messages.push({
                    role: 'system',
                    content: currentCharacter.prompt
                });
            }
            
            // Agregar historial de conversación (solo texto, sin archivos procesados)
            if (currentConversation && currentConversation.messages) {
                currentConversation.messages.forEach(msg => {
                    if (msg.role !== 'system' && !msg.isFile) {
                        messages.push({
                            role: msg.role === 'user' ? 'user' : 'assistant',
                            content: msg.content
                        });
                    }
                });
            }
            
            // Agregar mensaje actual (con archivos procesados)
            messages.push({
                role: 'user',
                content: message
            });

            const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: model,
                    messages: messages,
                    temperature: temperature,
                    max_tokens: maxTokens,
                    stream: false
                })
            });

            if (!response.ok) {
                throw new Error(`Error del servidor: ${response.status}`);
            }

            const data = await response.json();
            return data.choices[0].message.content;
        }

        // Funciones de interfaz de chat
        function addMessage(role, content) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.textContent = content;
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            
            // Actualizar conversación actual
            if (currentConversation) {
                if (!currentConversation.messages) {
                    currentConversation.messages = [];
                }
                currentConversation.messages.push({
                    role: role,
                    content: content,
                    timestamp: new Date().toISOString(),
                    isFile: false
                });
            }
        }

        function addImageMessage(role, file) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            // Crear elemento de imagen
            const img = document.createElement('img');
            img.src = file.data;
            img.alt = file.name;
            img.style.maxWidth = '200px';
            img.style.maxHeight = '200px';
            img.style.borderRadius = '8px';
            img.style.objectFit = 'cover';
            
            const caption = document.createElement('div');
            caption.textContent = `📷 ${file.name}`;
            caption.style.marginTop = '5px';
            caption.style.fontSize = '0.9em';
            caption.style.opacity = '0.8';
            
            messageDiv.appendChild(img);
            messageDiv.appendChild(caption);
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            
            // Actualizar conversación actual
            if (currentConversation) {
                if (!currentConversation.messages) {
                    currentConversation.messages = [];
                }
                currentConversation.messages.push({
                    role: role,
                    content: `[IMAGEN: ${file.name}]`,
                    timestamp: new Date().toISOString(),
                    isFile: true,
                    fileData: file.data
                });
            }
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').classList.add('show');
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').classList.remove('show');
        }

        function showError(message) {
            const chatContainer = document.getElementById('chatContainer');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'message system';
            errorDiv.innerHTML = `❌ ${message}`;
            chatContainer.appendChild(errorDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function clearChat() {
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = '<div class="message system">Chat limpiado.</div>';
        }

        // Funciones de conversaciones
        function newConversation() {
            const title = prompt('Nombre de la nueva conversación:');
            if (!title) return;
            
            const conversation = {
                id: Date.now().toString(),
                title: title,
                messages: [],
                character: currentCharacter ? currentCharacter.id : null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            conversations.unshift(conversation);
            currentConversation = conversation;
            
            clearChat();
            addMessage('system', `Nueva conversación: ${title}`);
            
            saveToStorage();
            loadConversations();
            closeModal('conversationsModal');
        }

        function loadConversation(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (!conversation) return;
            
            currentConversation = conversation;
            clearChat();
            
            // Cargar personaje de la conversación
            if (conversation.character) {
                const character = characters.find(c => c.id === conversation.character);
                if (character) {
                    currentCharacter = character;
                    addMessage('system', `Personaje activo: ${character.name}`);
                }
            }
            
            // Cargar mensajes
            if (conversation.messages) {
                conversation.messages.forEach(msg => {
                    if (msg.isFile && msg.fileData) {
                        // Recrear mensaje de archivo (imagen)
                        const chatContainer = document.getElementById('chatContainer');
                        const messageDiv = document.createElement('div');
                        messageDiv.className = `message ${msg.role}`;
                        
                        if (msg.fileData.startsWith('data:image/')) {
                            const img = document.createElement('img');
                            img.src = msg.fileData;
                            img.alt = msg.content;
                            img.style.maxWidth = '200px';
                            img.style.maxHeight = '200px';
                            img.style.borderRadius = '8px';
                            img.style.objectFit = 'cover';
                            
                            const caption = document.createElement('div');
                            caption.textContent = msg.content;
                            caption.style.marginTop = '5px';
                            caption.style.fontSize = '0.9em';
                            caption.style.opacity = '0.8';
                            
                            messageDiv.appendChild(img);
                            messageDiv.appendChild(caption);
                        } else {
                            messageDiv.textContent = msg.content;
                        }
                        
                        chatContainer.appendChild(messageDiv);
                    } else {
                        // Mensaje de texto normal
                        const messageDiv = document.createElement('div');
                        messageDiv.className = `message ${msg.role}`;
                        messageDiv.textContent = msg.content;
                        document.getElementById('chatContainer').appendChild(messageDiv);
                    }
                });
            }
            
            // Scroll al final
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.scrollTop = chatContainer.scrollHeight;
            
            updateConversationsList();
            closeModal('conversationsModal');
        }

        function deleteConversation(conversationId) {
            if (confirm('¿Estás seguro de eliminar esta conversación?')) {
                conversations = conversations.filter(c => c.id !== conversationId);
                
                if (currentConversation && currentConversation.id === conversationId) {
                    currentConversation = null;
                    clearChat();
                }
                
                saveToStorage();
                loadConversations();
            }
        }

        function saveCurrentConversation() {
            if (!currentConversation) return;
            
            currentConversation.updatedAt = new Date().toISOString();
            saveToStorage();
            loadConversations();
        }

        function loadConversations() {
            updateConversationsList();
        }

        function updateConversationsList() {
            const conversationList = document.getElementById('conversationList');
            conversationList.innerHTML = '';
            
            conversations.forEach(conversation => {
                const conversationDiv = document.createElement('div');
                conversationDiv.className = `conversation-item ${currentConversation && currentConversation.id === conversation.id ? 'active' : ''}`;
                
                const lastMessage = conversation.messages && conversation.messages.length > 0 
                    ? conversation.messages[conversation.messages.length - 1].content 
                    : 'Sin mensajes';
                
                conversationDiv.innerHTML = `
                    <div class="conversation-info">
                        <div class="conversation-title">${conversation.title}</div>
                        <div class="conversation-preview">${lastMessage.substring(0, 50)}...</div>
                    </div>
                    <div class="conversation-actions">
                        <button class="icon-btn" onclick="loadConversation('${conversation.id}')">📂</button>
                        <button class="icon-btn" onclick="deleteConversation('${conversation.id}')">🗑️</button>
                    </div>
                `;
                
                conversationList.appendChild(conversationDiv);
            });
        }

        // Funciones de personajes
        function createCharacter() {
            document.getElementById('characterModalTitle').textContent = 'Crear Personaje';
            document.getElementById('characterName').value = '';
            document.getElementById('characterDesc').value = '';
            document.getElementById('characterPrompt').value = '';
            document.getElementById('characterEmoji').value = '🤖';
            
            currentEditingCharacter = null;
            openModal('characterModal');
        }

        function editCharacter(characterId) {
            const character = characters.find(c => c.id === characterId);
            if (!character) return;
            
            document.getElementById('characterModalTitle').textContent = 'Editar Personaje';
            document.getElementById('characterName').value = character.name;
            document.getElementById('characterDesc').value = character.description;
            document.getElementById('characterPrompt').value = character.prompt;
            document.getElementById('characterEmoji').value = character.emoji;
            
            currentEditingCharacter = character;
            openModal('characterModal');
        }

        function saveCharacter() {
            const name = document.getElementById('characterName').value.trim();
            const description = document.getElementById('characterDesc').value.trim();
            const prompt = document.getElementById('characterPrompt').value.trim();
            const emoji = document.getElementById('characterEmoji').value.trim();
            
            if (!name || !prompt) {
                alert('El nombre y el prompt son obligatorios');
                return;
            }
            
            if (currentEditingCharacter) {
                // Editar personaje existente
                currentEditingCharacter.name = name;
                currentEditingCharacter.description = description;
                currentEditingCharacter.prompt = prompt;
                currentEditingCharacter.emoji = emoji;
            } else {
                // Crear nuevo personaje
                const character = {
                    id: Date.now().toString(),
                    name: name,
                    description: description,
                    prompt: prompt,
                    emoji: emoji
                };
                characters.push(character);
            }
            
            saveToStorage();
            loadCharacters();
            closeModal('characterModal');
        }

        function deleteCharacter(characterId) {
            if (characterId === 'default') {
                alert('No se puede eliminar el personaje por defecto');
                return;
            }
            
            if (confirm('¿Estás seguro de eliminar este personaje?')) {
                characters = characters.filter(c => c.id !== characterId);
                
                if (currentCharacter && currentCharacter.id === characterId) {
                    currentCharacter = characters[0];
                }
                
                saveToStorage();
                loadCharacters();
            }
        }

        function selectCharacter(characterId) {
            currentCharacter = characters.find(c => c.id === characterId);
            if (currentCharacter) {
                addMessage('system', `Personaje activo: ${currentCharacter.name}`);

                // Cargar automáticamente el prompt del personaje en el mensaje del sistema
                loadCharacterPrompt(currentCharacter);

                if (currentConversation) {
                    currentConversation.character = characterId;
                    saveToStorage();
                }
            }

            loadCharacters();
            closeModal('charactersModal');
        }

        function loadCharacters() {
            updateCharacterGrid();
        }

        function updateCharacterGrid() {
            const characterGrid = document.getElementById('characterGrid');
            characterGrid.innerHTML = '';
            
            characters.forEach(character => {
                const characterDiv = document.createElement('div');
                characterDiv.className = `character-card ${currentCharacter && currentCharacter.id === character.id ? 'active' : ''}`;
                
                characterDiv.innerHTML = `
                    <div class="character-avatar">${character.emoji}</div>
                    <div class="character-name">${character.name}</div>
                    <div class="character-desc">${character.description}</div>
                    <div class="character-actions" style="margin-top: 10px;">
                        <button class="icon-btn" onclick="selectCharacter('${character.id}')">✅</button>
                        <button class="icon-btn" onclick="editCharacter('${character.id}')">✏️</button>
                        ${character.id !== 'default' ? `<button class="icon-btn" onclick="deleteCharacter('${character.id}')">🗑️</button>` : ''}
                    </div>
                `;
                
                characterGrid.appendChild(characterDiv);
            });
        }

        // Funciones de archivos
        async function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            
            for (const file of files) {
                try {
                    const processedFile = await processFile(file);
                    attachedFiles.push(processedFile);
                } catch (error) {
                    showError(`Error procesando archivo ${file.name}: ${error.message}`);
                }
            }
            
            updateFilePreview();
        }

        // Función auxiliar para analizar imágenes
        async function analyzeImageBasics(file) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = function() {
                    const analysis = {
                        width: img.width,
                        height: img.height,
                        aspectRatio: (img.width / img.height).toFixed(2),
                        orientation: img.width > img.height ? 'horizontal' : img.height > img.width ? 'vertical' : 'cuadrada'
                    };
                    resolve(analysis);
                };
                img.onerror = function() {
                    resolve(null);
                };

                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            });
        }

        async function processFile(file) {
            return new Promise(async (resolve, reject) => {
                const reader = new FileReader();
                
                reader.onload = async function(e) {
                    const fileData = {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        lastModified: file.lastModified
                    };

                    if (file.type.startsWith('image/')) {
                        // Para imágenes, convertir a base64 y analizar propiedades básicas
                        fileData.data = e.target.result;

                        // Intentar obtener información básica de la imagen
                        const imageAnalysis = await analyzeImageBasics(file);

                        let analysisText = '';
                        if (imageAnalysis) {
                            analysisText = `
Dimensiones: ${imageAnalysis.width} x ${imageAnalysis.height} píxeles
Orientación: ${imageAnalysis.orientation}
Relación de aspecto: ${imageAnalysis.aspectRatio}:1`;
                        }

                        fileData.processedContent = `[IMAGEN: ${file.name}]
Tipo: ${file.type}
Tamaño: ${(file.size / 1024).toFixed(2)} KB
Última modificación: ${new Date(file.lastModified).toLocaleString()}${analysisText}

ANÁLISIS DISPONIBLE: Se ha adjuntado una imagen. Basándome en las propiedades técnicas:
- Nombre del archivo: "${file.name}" (puede indicar el contenido)
- Formato: ${file.type}${imageAnalysis ? `
- Es una imagen ${imageAnalysis.orientation}
- Tamaño: ${imageAnalysis.width}x${imageAnalysis.height} píxeles` : ''}

LIMITACIONES: Esta API no puede analizar el contenido visual directo de imágenes.
Para análisis visual completo, se recomienda usar modelos con capacidades de visión.

¿Qué tipo de análisis necesitas para esta imagen? Puedo ayudarte con:
1. Sugerencias basadas en el nombre del archivo
2. Recomendaciones de análisis según el tipo de imagen
3. Interpretación de las propiedades técnicas`;

                        resolve(fileData);
                        return;
                    }

                    if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                        // Para archivos de texto, leer el contenido completo
                        const textContent = e.target.result;
                        fileData.data = textContent;
                        fileData.processedContent = `[DOCUMENTO DE TEXTO: ${file.name}]
Contenido del archivo:
---
${textContent}
---
Por favor, analiza el contenido de este documento de texto.`;
                    } else if (file.type === 'application/pdf') {
                        // Para PDFs, almacenar como base64 (se procesará después)
                        fileData.data = e.target.result;
                        fileData.processedContent = `[PDF: ${file.name}] - Documento PDF adjunto para análisis. Nota: El contenido del PDF no puede ser leído directamente. Por favor, describe lo que puedas inferir del nombre del archivo.`;
                    } else if (file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
                        fileData.data = e.target.result;
                        fileData.processedContent = `[DOCUMENTO WORD: ${file.name}] - Documento de Word adjunto. Nota: El contenido no puede ser leído directamente. Por favor, describe lo que puedas inferir del nombre del archivo.`;
                    } else {
                        // Para otros tipos de archivo
                        fileData.data = e.target.result;
                        fileData.processedContent = `[ARCHIVO: ${file.name}] - Archivo de tipo ${file.type || 'desconocido'} adjunto. Por favor, describe lo que puedas inferir del nombre y tipo del archivo.`;
                    }

                    resolve(fileData);
                };
                
                reader.onerror = function() {
                    reject(new Error('Error leyendo el archivo'));
                };
                
                // Leer archivo según su tipo
                if (file.type.startsWith('image/')) {
                    reader.readAsDataURL(file);
                } else if (file.type === 'text/plain' || file.name.endsWith('.txt') || file.name.endsWith('.md') || file.name.endsWith('.csv')) {
                    // Leer como texto para archivos de texto plano
                    reader.readAsText(file, 'UTF-8');
                } else {
                    // Para otros tipos, leer como base64
                    reader.readAsDataURL(file);
                }
            });
        }

        function updateFilePreview() {
            const filePreview = document.getElementById('filePreview');
            filePreview.innerHTML = '';
            
            attachedFiles.forEach((file, index) => {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-item';
                
                let fileIcon = '📄';
                if (file.type.startsWith('image/')) {
                    fileIcon = '🖼️';
                    // Mostrar vista previa de imagen
                    if (file.data) {
                        fileDiv.innerHTML = `
                            <img src="${file.data}" alt="${file.name}" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                            <span>${file.name}</span>
                            <button class="icon-btn" onclick="removeFile(${index})">❌</button>
                        `;
                    }
                } else if (file.type === 'application/pdf') {
                    fileIcon = '📕';
                } else if (file.type === 'text/plain') {
                    fileIcon = '📝';
                }
                
                if (!file.type.startsWith('image/') || !file.data) {
                    fileDiv.innerHTML = `
                        <span>${fileIcon}</span>
                        <span>${file.name}</span>
                        <button class="icon-btn" onclick="removeFile(${index})">❌</button>
                    `;
                }
                
                filePreview.appendChild(fileDiv);
            });
        }

        function removeFile(index) {
            attachedFiles.splice(index, 1);
            updateFilePreview();
        }

        function clearAttachedFiles() {
            attachedFiles = [];
            updateFilePreview();
            document.getElementById('fileInput').value = '';
        }

        // Funciones de utilidad
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // Funciones de modales
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        function openSettings() {
            openModal('settingsModal');
        }

        function openConversations() {
            loadConversations();
            openModal('conversationsModal');
        }

        function openCharacters() {
            loadCharacters();
            openModal('charactersModal');
        }

        // Variables globales adicionales
        let currentEditingCharacter = null;
    </script>
</body>
</html>