{"name": "lm-studio-chat-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"setup": "node setup.js", "start": "expo start", "start:clear": "expo start --clear", "start:tunnel": "expo start --tunnel", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "doctor": "expo doctor"}, "dependencies": {"expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-paper": "^5.10.6", "react-native-vector-icons": "^10.0.0", "expo-document-picker": "~11.5.4", "expo-image-picker": "~14.3.2", "expo-file-system": "~15.4.5", "expo-sharing": "~11.5.0", "expo-clipboard": "~4.3.1", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "react-native-async-storage": "^1.19.3", "react-native-modal": "^13.0.1", "react-native-slider": "^0.11.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}