{"name": "lm-studio-chat-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"setup": "node setup.js", "start": "expo start", "start:clear": "expo start --clear", "start:tunnel": "expo start --tunnel", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "doctor": "expo doctor"}, "dependencies": {"expo": "~50.0.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-paper": "^5.12.3", "react-native-vector-icons": "^10.0.3", "expo-document-picker": "~11.10.1", "expo-image-picker": "~14.7.1", "expo-file-system": "~16.0.6", "expo-sharing": "~12.0.1", "expo-clipboard": "~5.0.1", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-native-async-storage/async-storage": "1.21.0", "react-native-modal": "^13.0.1", "@react-native-community/slider": "^4.4.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "^12.3.6"}, "private": true}