<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 LM Studio Chat - Funcional</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #1a1a2e;
            color: white;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 1.2em;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.2);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease;
        }

        .message.user {
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-left: auto;
            color: white;
        }

        .message.ai {
            background: rgba(255, 255, 255, 0.1);
            margin-right: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message.system {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            text-align: center;
            margin: 10px auto;
            max-width: 90%;
            font-size: 0.9em;
        }

        .input-container {
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            padding: 12px 50px 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            resize: none;
            min-height: 48px;
            max-height: 120px;
            outline: none;
            backdrop-filter: blur(10px);
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .attach-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            font-size: 20px;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .attach-btn:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .send-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.1);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: #1a1a2e;
            padding: 25px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.3em;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .success-message {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
            color: #51cf66;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        .error-message {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.5);
            color: #ff6b6b;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            display: inline-block;
            margin-left: 8px;
        }

        .status-indicator.offline {
            background: #dc3545;
        }

        .character-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .character-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .character-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .character-card.active {
            border-color: #667eea;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
            background: rgba(102, 126, 234, 0.2);
        }

        .character-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin: 0 auto 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .character-name {
            font-size: 0.9em;
            font-weight: 500;
            margin-bottom: 5px;
            color: white;
        }

        .character-desc {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.3;
            margin-bottom: 10px;
        }

        .character-actions {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-top: 10px;
        }

        .icon-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .icon-btn:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .character-status {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            display: none;
        }

        .character-card.active .character-status {
            display: block;
        }

        .conversation-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            margin-top: 15px;
        }

        .conversation-item {
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .conversation-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .conversation-item.active {
            background: rgba(102, 126, 234, 0.3);
            border-left: 4px solid #667eea;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-title {
            font-weight: 500;
            margin-bottom: 4px;
            color: white;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .conversation-character {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 4px;
        }

        .conversation-preview {
            font-size: 0.9em;
            color: rgba(255, 255, 255, 0.7);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-meta {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.5);
            margin-top: 4px;
        }

        .conversation-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .conversation-item:hover .conversation-actions {
            opacity: 1;
        }

        .conversation-actions .icon-btn {
            font-size: 16px;
            padding: 8px;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>🚀 LM Studio Chat <span class="status-indicator offline" id="statusIndicator"></span></h1>
            <div class="header-actions">
                <button class="btn" id="configBtn">⚙️ Config</button>
                <button class="btn" id="chatsBtn">💬 Chats</button>
                <button class="btn" id="charactersBtn">👤 Personajes</button>
            </div>
        </div>

        <div class="main-content">
            <div class="chat-container" id="chatContainer">
                <div class="message system">
                    🚀 <strong>¡Bienvenido a LM Studio Chat Funcional!</strong><br><br>
                    
                    <strong>📋 Instrucciones:</strong><br>
                    1. Haz clic en <strong>"⚙️ Config"</strong> para configurar<br>
                    2. Inicia LM Studio y carga un modelo<br>
                    3. Prueba la conexión<br>
                    4. ¡Comienza a chatear!<br><br>
                    
                    <em>💡 Todos los botones deberían funcionar correctamente.</em>
                </div>
            </div>

            <div class="input-container">
                <div class="input-group">
                    <div class="input-wrapper">
                        <textarea 
                            class="message-input" 
                            id="messageInput" 
                            placeholder="Escribe tu mensaje..."
                            rows="1"
                        ></textarea>
                        <button class="attach-btn" id="attachBtn">📎</button>
                        <input type="file" id="fileInput" style="display:none" multiple>
                    </div>
                    <button class="send-btn" id="sendBtn">➤</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Configuración -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Configuración</h2>
                <button class="close-btn" id="closeSettingsBtn">&times;</button>
            </div>
            <div class="form-group">
                <label class="form-label">Servidor LM Studio</label>
                <input type="text" class="form-input" id="serverUrl" value="http://localhost:1234">
            </div>
            <div class="form-group">
                <label class="form-label">Modelo Actual</label>
                <select class="form-select" id="modelSelect">
                    <option value="">Seleccionar modelo...</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">Mensaje del Sistema</label>
                <textarea class="form-textarea" id="systemMessage" placeholder="Eres un asistente útil y amigable..."></textarea>
                <div style="margin-top: 10px; display: flex; gap: 10px;">
                    <button class="btn" id="applySystemMessageBtn">✅ Aplicar Mensaje</button>
                    <button class="btn" id="clearSystemMessageBtn">🗑️ Limpiar</button>
                </div>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="testConnectionBtn">🔗 Probar Conexión</button>
                <button class="btn btn-primary" id="loadModelsBtn">🔄 Cargar Modelos</button>
            </div>
            <div id="connectionStatus"></div>
        </div>
    </div>

    <!-- Modal de Chats -->
    <div class="modal" id="chatsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Gestión de Conversaciones</h2>
                <button class="close-btn" id="closeChatsBtn">&times;</button>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="newChatBtn">+ Nueva Conversación</button>
                <button class="btn" id="importChatsBtn">📥 Importar</button>
                <button class="btn" id="exportAllChatsBtn">📤 Exportar Todo</button>
                <input type="file" id="importFileInput" style="display:none" accept=".txt,.json">
            </div>
            <div class="conversation-list" id="conversationList">
                <!-- Las conversaciones se cargarán aquí -->
            </div>
        </div>
    </div>

    <!-- Modal de Editar Conversación -->
    <div class="modal" id="editChatModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Editar Conversación</h2>
                <button class="close-btn" id="closeEditChatBtn">&times;</button>
            </div>
            <div class="form-group">
                <label class="form-label">Nombre de la Conversación</label>
                <input type="text" class="form-input" id="chatName" placeholder="Nombre de la conversación">
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="saveChatNameBtn">💾 Guardar</button>
                <button class="btn" id="cancelEditChatBtn">❌ Cancelar</button>
            </div>
        </div>
    </div>

    <!-- Modal de Personajes -->
    <div class="modal" id="charactersModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Personajes</h2>
                <button class="close-btn" id="closeCharactersBtn">&times;</button>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="createCharacterBtn">+ Crear Personaje</button>
            </div>
            <div class="character-grid" id="characterGrid">
                <!-- Los personajes se cargarán aquí -->
            </div>
        </div>
    </div>

    <!-- Modal de Crear/Editar Personaje -->
    <div class="modal" id="characterEditModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="characterEditTitle">Crear Personaje</h2>
                <button class="close-btn" id="closeCharacterEditBtn">&times;</button>
            </div>
            <div class="form-group">
                <label class="form-label">Nombre</label>
                <input type="text" class="form-input" id="characterName" placeholder="Nombre del personaje">
            </div>
            <div class="form-group">
                <label class="form-label">Descripción</label>
                <input type="text" class="form-input" id="characterDesc" placeholder="Descripción breve">
            </div>
            <div class="form-group">
                <label class="form-label">Prompt del Sistema</label>
                <textarea class="form-textarea" id="characterPrompt" placeholder="Eres un asistente especializado en..." style="min-height: 120px;"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">Emoji/Avatar</label>
                <input type="text" class="form-input" id="characterEmoji" placeholder="🤖" maxlength="2">
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="saveCharacterBtn">💾 Guardar</button>
                <button class="btn" id="cancelCharacterBtn">❌ Cancelar</button>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 JavaScript iniciando...');

        // Variables globales
        let isConnected = false;
        let models = [];
        let currentSystemMessage = '';
        let characters = [];
        let currentCharacter = null;
        let editingCharacter = null;
        let conversations = [];
        let currentConversation = null;

        // Función para agregar mensajes
        function addMessage(role, content) {
            const chatContainer = document.getElementById('chatContainer');
            if (!chatContainer) {
                console.error('chatContainer no encontrado');
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.innerHTML = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Funciones de modales
        function openModal(modalId) {
            console.log('🔓 Abriendo modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                console.log('✅ Modal abierto:', modalId);
                addMessage('system', `✅ Modal "${modalId}" abierto correctamente`);
            } else {
                console.error('❌ Modal no encontrado:', modalId);
                alert('Error: Modal no encontrado - ' + modalId);
            }
        }

        function closeModal(modalId) {
            console.log('🔒 Cerrando modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                console.log('✅ Modal cerrado:', modalId);
            } else {
                console.error('❌ Modal no encontrado:', modalId);
            }
        }

        // Función para probar conexión
        async function testConnection() {
            const serverUrl = document.getElementById('serverUrl').value;
            const statusDiv = document.getElementById('connectionStatus');

            try {
                statusDiv.innerHTML = '<div class="success-message">Probando conexión...</div>';
                console.log('🔗 Probando conexión a:', serverUrl);

                const response = await fetch(`${serverUrl}/v1/models`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    isConnected = true;
                    statusDiv.innerHTML = '<div class="success-message">✅ Conexión exitosa!</div>';
                    document.getElementById('statusIndicator').classList.remove('offline');
                    console.log('✅ Conexión exitosa');
                    addMessage('system', '✅ Conectado a LM Studio exitosamente');
                } else {
                    throw new Error(`Error HTTP: ${response.status}`);
                }
            } catch (error) {
                isConnected = false;
                statusDiv.innerHTML = '<div class="error-message">❌ Error de conexión. Verifica que LM Studio esté ejecutándose.</div>';
                document.getElementById('statusIndicator').classList.add('offline');
                console.error('❌ Error de conexión:', error);
                addMessage('system', '❌ Error de conexión con LM Studio');
            }
        }

        // Función para cargar modelos
        async function loadModels() {
            const serverUrl = document.getElementById('serverUrl').value;
            const modelSelect = document.getElementById('modelSelect');

            try {
                console.log('📥 Cargando modelos...');
                const response = await fetch(`${serverUrl}/v1/models`);

                if (response.ok) {
                    const data = await response.json();
                    models = data.data || [];

                    modelSelect.innerHTML = '<option value="">Seleccionar modelo...</option>';
                    models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = model.id;
                        modelSelect.appendChild(option);
                    });

                    console.log('✅ Modelos cargados:', models.length);
                    addMessage('system', `✅ ${models.length} modelos cargados`);
                } else {
                    throw new Error('Error cargando modelos');
                }
            } catch (error) {
                console.error('❌ Error cargando modelos:', error);
                addMessage('system', '❌ Error cargando modelos');
            }
        }

        // Función para enviar mensaje
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            if (!isConnected) {
                addMessage('system', '❌ No hay conexión con LM Studio');
                return;
            }

            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel) {
                addMessage('system', '❌ Selecciona un modelo primero');
                return;
            }

            // Crear nueva conversación si no existe
            if (!currentConversation) {
                const title = `Chat ${conversations.length + 1}`;
                currentConversation = {
                    id: Date.now().toString(),
                    title: title,
                    messages: [],
                    characterId: currentCharacter ? currentCharacter.id : null,
                    characterName: currentCharacter ? currentCharacter.name : null,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                conversations.unshift(currentConversation);
                saveConversations();
            }

            // Agregar mensaje del usuario
            addMessage('user', message);
            input.value = '';

            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const systemMsg = document.getElementById('systemMessage').value;

                const messages = [];
                if (systemMsg) {
                    messages.push({ role: 'system', content: systemMsg });
                }
                messages.push({ role: 'user', content: message });

                const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        messages: messages,
                        temperature: 0.7,
                        max_tokens: 2048
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const aiResponse = data.choices[0].message.content;
                    addMessage('ai', aiResponse);

                    // Guardar conversación automáticamente
                    saveCurrentConversation();
                } else {
                    throw new Error(`Error HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('❌ Error enviando mensaje:', error);
                addMessage('system', '❌ Error enviando mensaje');
            }
        }

        // Funciones de persistencia
        function saveSettings() {
            const settings = {
                serverUrl: document.getElementById('serverUrl').value,
                systemMessage: document.getElementById('systemMessage').value,
                currentCharacterId: currentCharacter ? currentCharacter.id : null,
                isConnected: isConnected
            };
            localStorage.setItem('lmstudio_settings', JSON.stringify(settings));
        }

        function loadSettings() {
            const savedSettings = localStorage.getItem('lmstudio_settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                document.getElementById('serverUrl').value = settings.serverUrl || 'http://localhost:1234';
                document.getElementById('systemMessage').value = settings.systemMessage || '';
                currentSystemMessage = settings.systemMessage || '';
                return settings;
            }
            return null;
        }

        // Funciones de mensaje del sistema
        function applySystemMessage() {
            const systemMsg = document.getElementById('systemMessage').value.trim();
            currentSystemMessage = systemMsg;

            if (systemMsg) {
                addMessage('system', `✅ Mensaje del sistema aplicado: "${systemMsg.substring(0, 50)}${systemMsg.length > 50 ? '...' : ''}"`);
            } else {
                addMessage('system', '✅ Mensaje del sistema limpiado');
            }

            saveSettings();
        }

        function clearSystemMessage() {
            document.getElementById('systemMessage').value = '';
            currentSystemMessage = '';
            addMessage('system', '✅ Mensaje del sistema limpiado');
            saveSettings();
        }

        // Funciones de conversaciones
        function loadConversations() {
            const savedConversations = localStorage.getItem('lmstudio_conversations');
            if (savedConversations) {
                conversations = JSON.parse(savedConversations);
            }
            updateConversationsList();
        }

        function saveConversations() {
            localStorage.setItem('lmstudio_conversations', JSON.stringify(conversations));
        }

        function createNewConversation() {
            const title = prompt('Nombre de la nueva conversación:') || `Chat ${conversations.length + 1}`;

            const newConversation = {
                id: Date.now().toString(),
                title: title,
                messages: [],
                characterId: currentCharacter ? currentCharacter.id : null,
                characterName: currentCharacter ? currentCharacter.name : null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            conversations.unshift(newConversation);
            currentConversation = newConversation;

            // Limpiar chat actual
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = '';
            addMessage('system', `✅ Nueva conversación "${title}" creada`);

            saveConversations();
            updateConversationsList();
            closeModal('chatsModal');
        }

        function loadConversation(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (!conversation) return;

            // Guardar conversación actual antes de cambiar
            if (currentConversation) {
                saveCurrentConversation();
            }

            currentConversation = conversation;

            // Limpiar chat
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = '';

            // Cargar personaje de la conversación
            if (conversation.characterId) {
                const character = characters.find(c => c.id === conversation.characterId);
                if (character) {
                    currentCharacter = character;
                    document.getElementById('systemMessage').value = character.prompt;
                    currentSystemMessage = character.prompt;
                    updateCharacterGrid();
                }
            }

            // Cargar mensajes
            if (conversation.messages && conversation.messages.length > 0) {
                conversation.messages.forEach(msg => {
                    addMessage(msg.role, msg.content);
                });
            } else {
                addMessage('system', `📖 Conversación "${conversation.title}" cargada`);
            }

            updateConversationsList();
            closeModal('chatsModal');
        }

        function saveCurrentConversation() {
            if (!currentConversation) return;

            // Obtener mensajes del chat actual
            const chatContainer = document.getElementById('chatContainer');
            const messageElements = chatContainer.querySelectorAll('.message:not(.system)');

            currentConversation.messages = Array.from(messageElements).map(el => ({
                role: el.classList.contains('user') ? 'user' : 'ai',
                content: el.textContent || el.innerHTML,
                timestamp: new Date().toISOString()
            }));

            currentConversation.updatedAt = new Date().toISOString();
            currentConversation.characterId = currentCharacter ? currentCharacter.id : null;
            currentConversation.characterName = currentCharacter ? currentCharacter.name : null;

            saveConversations();
        }

        function updateConversationsList() {
            const list = document.getElementById('conversationList');

            if (conversations.length === 0) {
                list.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">💬</div>
                        <div>No hay conversaciones</div>
                        <div style="margin-top: 8px; font-size: 0.9em;">Crea una nueva conversación para empezar</div>
                    </div>
                `;
                return;
            }

            list.innerHTML = '';

            conversations.forEach(conversation => {
                const item = document.createElement('div');
                item.className = `conversation-item ${currentConversation && currentConversation.id === conversation.id ? 'active' : ''}`;

                const lastMessage = conversation.messages && conversation.messages.length > 0
                    ? conversation.messages[conversation.messages.length - 1].content
                    : 'Sin mensajes';

                const characterInfo = conversation.characterName
                    ? `👤 ${conversation.characterName}`
                    : '👤 Sin personaje';

                const messageCount = conversation.messages ? conversation.messages.length : 0;
                const date = new Date(conversation.updatedAt).toLocaleDateString();

                item.innerHTML = `
                    <div class="conversation-info">
                        <div class="conversation-title">
                            💬 ${conversation.title}
                        </div>
                        <div class="conversation-character">${characterInfo}</div>
                        <div class="conversation-preview">${lastMessage.substring(0, 60)}${lastMessage.length > 60 ? '...' : ''}</div>
                        <div class="conversation-meta">${messageCount} mensajes • ${date}</div>
                    </div>
                    <div class="conversation-actions">
                        <button class="icon-btn" onclick="loadConversation('${conversation.id}')" title="Abrir">📂</button>
                        <button class="icon-btn" onclick="editConversationName('${conversation.id}')" title="Editar">✏️</button>
                        <button class="icon-btn" onclick="exportConversation('${conversation.id}')" title="Exportar">📤</button>
                        <button class="icon-btn" onclick="deleteConversation('${conversation.id}')" title="Eliminar">🗑️</button>
                    </div>
                `;

                list.appendChild(item);
            });
        }

        let editingConversationId = null;

        function editConversationName(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (conversation) {
                editingConversationId = conversationId;
                document.getElementById('chatName').value = conversation.title;
                openModal('editChatModal');
            }
        }

        function saveChatName() {
            if (!editingConversationId) return;

            const newName = document.getElementById('chatName').value.trim();
            if (!newName) {
                alert('El nombre no puede estar vacío');
                return;
            }

            const conversation = conversations.find(c => c.id === editingConversationId);
            if (conversation) {
                conversation.title = newName;
                conversation.updatedAt = new Date().toISOString();
                saveConversations();
                updateConversationsList();
                addMessage('system', `✅ Conversación renombrada a "${newName}"`);
            }

            closeModal('editChatModal');
        }

        function deleteConversation(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (!conversation) return;

            if (confirm(`¿Estás seguro de eliminar la conversación "${conversation.title}"?`)) {
                conversations = conversations.filter(c => c.id !== conversationId);

                if (currentConversation && currentConversation.id === conversationId) {
                    currentConversation = null;
                    const chatContainer = document.getElementById('chatContainer');
                    chatContainer.innerHTML = '';
                    addMessage('system', '✅ Conversación eliminada');
                }

                saveConversations();
                updateConversationsList();
            }
        }

        function exportConversation(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (!conversation) return;

            const exportData = {
                title: conversation.title,
                character: conversation.characterName || 'Sin personaje',
                createdAt: conversation.createdAt,
                updatedAt: conversation.updatedAt,
                messages: conversation.messages || []
            };

            let content = `=== ${exportData.title} ===\n`;
            content += `Personaje: ${exportData.character}\n`;
            content += `Creado: ${new Date(exportData.createdAt).toLocaleString()}\n`;
            content += `Actualizado: ${new Date(exportData.updatedAt).toLocaleString()}\n`;
            content += `Mensajes: ${exportData.messages.length}\n\n`;
            content += "=".repeat(50) + "\n\n";

            exportData.messages.forEach((msg, index) => {
                const role = msg.role === 'user' ? 'Usuario' : 'IA';
                content += `[${index + 1}] ${role}:\n${msg.content}\n\n`;
            });

            downloadTextFile(`${conversation.title}.txt`, content);
            addMessage('system', `✅ Conversación "${conversation.title}" exportada`);
        }

        function exportAllConversations() {
            if (conversations.length === 0) {
                alert('No hay conversaciones para exportar');
                return;
            }

            const exportData = {
                exportDate: new Date().toISOString(),
                totalConversations: conversations.length,
                conversations: conversations
            };

            downloadTextFile('todas_las_conversaciones.json', JSON.stringify(exportData, null, 2));
            addMessage('system', `✅ ${conversations.length} conversaciones exportadas`);
        }

        function importConversations() {
            document.getElementById('importFileInput').click();
        }

        function handleImportFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const content = e.target.result;
                    let importedData;

                    if (file.name.endsWith('.json')) {
                        importedData = JSON.parse(content);

                        if (importedData.conversations && Array.isArray(importedData.conversations)) {
                            const importCount = importedData.conversations.length;

                            // Agregar conversaciones importadas
                            importedData.conversations.forEach(conv => {
                                conv.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
                                conv.title = conv.title + ' (Importado)';
                            });

                            conversations = [...importedData.conversations, ...conversations];
                            saveConversations();
                            updateConversationsList();
                            addMessage('system', `✅ ${importCount} conversaciones importadas`);
                        } else {
                            throw new Error('Formato de archivo no válido');
                        }
                    } else {
                        alert('Solo se admiten archivos .json para importar');
                    }
                } catch (error) {
                    alert('Error al importar archivo: ' + error.message);
                }
            };

            reader.readAsText(file);
            event.target.value = ''; // Limpiar input
        }

        function downloadTextFile(filename, content) {
            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Funciones de personajes
        function loadCharacters() {
            // Cargar personajes desde localStorage
            const savedCharacters = localStorage.getItem('lmstudio_characters');
            if (savedCharacters) {
                characters = JSON.parse(savedCharacters);
            } else {
                // Crear personajes por defecto
                characters = [
                    {
                        id: 'default',
                        name: 'Asistente General',
                        description: 'Asistente útil y amigable',
                        prompt: 'Eres un asistente útil y amigable. Responde de manera clara y concisa.',
                        emoji: '🤖'
                    },
                    {
                        id: 'teacher',
                        name: 'Profesor',
                        description: 'Educador paciente y didáctico',
                        prompt: 'Eres un profesor experimentado y paciente. Explicas conceptos de manera clara, usas ejemplos y te aseguras de que el estudiante comprenda antes de continuar.',
                        emoji: '👨‍🏫'
                    },
                    {
                        id: 'programmer',
                        name: 'Programador',
                        description: 'Experto en desarrollo de software',
                        prompt: 'Eres un programador senior con amplia experiencia. Ayudas con código, debugging, mejores prácticas y arquitectura de software. Siempre proporcionas ejemplos de código cuando es relevante.',
                        emoji: '👨‍💻'
                    },
                    {
                        id: 'creative',
                        name: 'Creativo',
                        description: 'Asistente creativo e imaginativo',
                        prompt: 'Eres un asistente creativo e imaginativo. Te especializas en escritura creativa, brainstorming, ideas innovadoras y soluciones fuera de lo convencional.',
                        emoji: '🎨'
                    }
                ];
                saveCharacters();
            }
            updateCharacterGrid();
        }

        function saveCharacters() {
            localStorage.setItem('lmstudio_characters', JSON.stringify(characters));
        }

        function updateCharacterGrid() {
            const grid = document.getElementById('characterGrid');
            grid.innerHTML = '';

            characters.forEach(character => {
                const card = document.createElement('div');
                card.className = `character-card ${currentCharacter && currentCharacter.id === character.id ? 'active' : ''}`;

                card.innerHTML = `
                    <div class="character-status"></div>
                    <div class="character-avatar">${character.emoji}</div>
                    <div class="character-name">${character.name}</div>
                    <div class="character-desc">${character.description}</div>
                    <div class="character-actions">
                        <button class="icon-btn" onclick="selectCharacter('${character.id}')" title="Seleccionar">✅</button>
                        <button class="icon-btn" onclick="editCharacter('${character.id}')" title="Editar">✏️</button>
                        ${character.id !== 'default' ? `<button class="icon-btn" onclick="deleteCharacter('${character.id}')" title="Eliminar">🗑️</button>` : ''}
                    </div>
                `;

                grid.appendChild(card);
            });
        }

        function selectCharacter(characterId) {
            const character = characters.find(c => c.id === characterId);
            if (character) {
                currentCharacter = character;

                // Cargar automáticamente el prompt en el mensaje del sistema
                document.getElementById('systemMessage').value = character.prompt;
                currentSystemMessage = character.prompt;

                updateCharacterGrid();
                addMessage('system', `✅ Personaje "${character.name}" seleccionado y cargado en mensaje del sistema`);

                // Guardar configuración
                saveSettings();

                console.log('✅ Personaje seleccionado:', character.name);
            }
        }

        function createCharacter() {
            editingCharacter = null;
            document.getElementById('characterEditTitle').textContent = 'Crear Personaje';
            document.getElementById('characterName').value = '';
            document.getElementById('characterDesc').value = '';
            document.getElementById('characterPrompt').value = '';
            document.getElementById('characterEmoji').value = '🤖';
            openModal('characterEditModal');
        }

        function editCharacter(characterId) {
            const character = characters.find(c => c.id === characterId);
            if (character) {
                editingCharacter = character;
                document.getElementById('characterEditTitle').textContent = 'Editar Personaje';
                document.getElementById('characterName').value = character.name;
                document.getElementById('characterDesc').value = character.description;
                document.getElementById('characterPrompt').value = character.prompt;
                document.getElementById('characterEmoji').value = character.emoji;
                openModal('characterEditModal');
            }
        }

        function saveCharacter() {
            const name = document.getElementById('characterName').value.trim();
            const description = document.getElementById('characterDesc').value.trim();
            const prompt = document.getElementById('characterPrompt').value.trim();
            const emoji = document.getElementById('characterEmoji').value.trim();

            if (!name || !prompt) {
                alert('El nombre y el prompt son obligatorios');
                return;
            }

            if (editingCharacter) {
                // Editar personaje existente
                editingCharacter.name = name;
                editingCharacter.description = description;
                editingCharacter.prompt = prompt;
                editingCharacter.emoji = emoji;
                addMessage('system', `✅ Personaje "${name}" actualizado`);
            } else {
                // Crear nuevo personaje
                const newCharacter = {
                    id: Date.now().toString(),
                    name: name,
                    description: description,
                    prompt: prompt,
                    emoji: emoji
                };
                characters.push(newCharacter);
                addMessage('system', `✅ Personaje "${name}" creado`);
            }

            saveCharacters();
            updateCharacterGrid();
            closeModal('characterEditModal');
        }

        function deleteCharacter(characterId) {
            if (characterId === 'default') {
                alert('No se puede eliminar el personaje por defecto');
                return;
            }

            const character = characters.find(c => c.id === characterId);
            if (character && confirm(`¿Estás seguro de eliminar el personaje "${character.name}"?`)) {
                characters = characters.filter(c => c.id !== characterId);

                if (currentCharacter && currentCharacter.id === characterId) {
                    currentCharacter = characters[0];
                    selectCharacter(currentCharacter.id);
                }

                saveCharacters();
                updateCharacterGrid();
                addMessage('system', `✅ Personaje "${character.name}" eliminado`);
            }
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 DOM cargado, configurando event listeners...');

            // Botones del header
            document.getElementById('configBtn').addEventListener('click', function() {
                console.log('🔧 Botón Config clickeado');
                openModal('settingsModal');
            });

            document.getElementById('chatsBtn').addEventListener('click', function() {
                console.log('💬 Botón Chats clickeado');
                openModal('chatsModal');
            });

            document.getElementById('charactersBtn').addEventListener('click', function() {
                console.log('👤 Botón Personajes clickeado');
                openModal('charactersModal');
            });

            // Botones de cerrar modales
            document.getElementById('closeSettingsBtn').addEventListener('click', function() {
                closeModal('settingsModal');
            });

            document.getElementById('closeChatsBtn').addEventListener('click', function() {
                closeModal('chatsModal');
            });

            document.getElementById('closeEditChatBtn').addEventListener('click', function() {
                closeModal('editChatModal');
            });

            // Botones de gestión de chats
            document.getElementById('newChatBtn').addEventListener('click', createNewConversation);
            document.getElementById('importChatsBtn').addEventListener('click', importConversations);
            document.getElementById('exportAllChatsBtn').addEventListener('click', exportAllConversations);
            document.getElementById('saveChatNameBtn').addEventListener('click', saveChatName);
            document.getElementById('cancelEditChatBtn').addEventListener('click', function() {
                closeModal('editChatModal');
            });

            // Input de importar archivos
            document.getElementById('importFileInput').addEventListener('change', handleImportFile);

            document.getElementById('closeCharactersBtn').addEventListener('click', function() {
                closeModal('charactersModal');
            });

            document.getElementById('closeCharacterEditBtn').addEventListener('click', function() {
                closeModal('characterEditModal');
            });

            // Botones de personajes
            document.getElementById('createCharacterBtn').addEventListener('click', createCharacter);
            document.getElementById('saveCharacterBtn').addEventListener('click', saveCharacter);
            document.getElementById('cancelCharacterBtn').addEventListener('click', function() {
                closeModal('characterEditModal');
            });

            // Botones de configuración
            document.getElementById('testConnectionBtn').addEventListener('click', testConnection);
            document.getElementById('loadModelsBtn').addEventListener('click', loadModels);
            document.getElementById('applySystemMessageBtn').addEventListener('click', applySystemMessage);
            document.getElementById('clearSystemMessageBtn').addEventListener('click', clearSystemMessage);

            // Botón de enviar
            document.getElementById('sendBtn').addEventListener('click', sendMessage);

            // Enter en el input
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Botón de adjuntar
            document.getElementById('attachBtn').addEventListener('click', function() {
                document.getElementById('fileInput').click();
            });

            // Cerrar modales al hacer clic fuera
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal')) {
                    e.target.classList.remove('active');
                }
            });

            // Guardar configuración automáticamente
            document.getElementById('serverUrl').addEventListener('change', saveSettings);
            document.getElementById('systemMessage').addEventListener('change', saveSettings);

            // Guardar conversación antes de cerrar la ventana
            window.addEventListener('beforeunload', function() {
                if (currentConversation) {
                    saveCurrentConversation();
                }
                saveSettings();
            });

            // Cargar configuraciones guardadas
            const settings = loadSettings();

            // Inicializar personajes
            loadCharacters();

            // Cargar conversaciones
            loadConversations();

            // Cargar personaje guardado
            if (settings && settings.currentCharacterId) {
                const character = characters.find(c => c.id === settings.currentCharacterId);
                if (character) {
                    selectCharacter(settings.currentCharacterId);
                }
            }

            // Crear conversación inicial si no hay ninguna
            if (conversations.length === 0) {
                currentConversation = {
                    id: Date.now().toString(),
                    title: 'Chat Principal',
                    messages: [],
                    characterId: currentCharacter ? currentCharacter.id : null,
                    characterName: currentCharacter ? currentCharacter.name : null,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                conversations.push(currentConversation);
                saveConversations();
            } else {
                // Cargar la conversación más reciente
                currentConversation = conversations[0];
            }

            console.log('✅ Event listeners configurados');
            addMessage('system', '✅ Aplicación lista - Todos los botones funcionando');
            addMessage('system', `👤 ${characters.length} personajes disponibles`);

            // Test automático
            setTimeout(() => {
                console.log('🧪 Test automático: Verificando funcionalidad...');
                addMessage('system', '🧪 Test: JavaScript funcionando correctamente');
            }, 1000);
        });

        console.log('✅ JavaScript cargado completamente');
    </script>
</body>
</html>
