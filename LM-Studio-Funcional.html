<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 LM Studio Chat - Funcional</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #1a1a2e;
            color: white;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header-left {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .header h1 {
            font-size: 1.2em;
            font-weight: 600;
            margin: 0;
        }

        .status-bar {
            display: flex;
            gap: 20px;
            font-size: 0.9em;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.85em;
        }

        .status-value {
            color: white;
            font-weight: 500;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .status-value.active {
            color: #51cf66;
        }

        .status-value.inactive {
            color: rgba(255, 255, 255, 0.5);
        }

        /* Dropdown Styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .dropdown-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background: rgba(0, 0, 0, 0.95);
            min-width: 180px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            margin-top: 5px;
            overflow: hidden;
        }

        .dropdown-content.show {
            display: block;
            animation: dropdownFadeIn 0.3s ease;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            display: block;
            width: 100%;
            padding: 12px 16px;
            background: none;
            border: none;
            color: white;
            text-align: left;
            cursor: pointer;
            transition: background 0.3s ease;
            font-size: 14px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: rgba(102, 126, 234, 0.2);
        }

        /* Mobile Optimization */
        @media (max-width: 768px) {
            .header {
                padding: 10px;
                flex-direction: column;
                gap: 10px;
            }

            .header-left {
                align-items: center;
                text-align: center;
            }

            .header h1 {
                font-size: 1.1em;
            }

            .status-bar {
                flex-direction: column;
                gap: 8px;
                align-items: center;
            }

            .status-item {
                min-width: 200px;
                justify-content: center;
            }

            .header-actions {
                display: flex;
                gap: 10px;
                justify-content: center;
                flex-wrap: wrap;
            }

            .btn {
                padding: 8px 12px;
                font-size: 13px;
            }

            .dropdown-content {
                right: auto;
                left: 50%;
                transform: translateX(-50%);
                min-width: 200px;
            }

            .chat-container {
                height: calc(100vh - 200px);
                padding: 10px;
            }

            .message {
                max-width: 95%;
                padding: 12px;
                font-size: 14px;
            }

            .message-actions {
                position: static;
                display: flex;
                justify-content: center;
                margin-top: 8px;
                background: rgba(0, 0, 0, 0.6);
                border-radius: 8px;
                padding: 6px;
            }

            .message:hover .message-actions {
                display: flex;
            }

            .input-container {
                padding: 10px;
            }

            .input-group {
                flex-direction: column;
                gap: 8px;
            }

            .message-input {
                min-height: 60px;
            }

            .send-btn {
                width: 100%;
                padding: 12px;
            }

            .modal-content {
                width: 95%;
                max-width: none;
                margin: 10px;
                max-height: 90vh;
                overflow-y: auto;
            }

            .form-group {
                margin-bottom: 15px;
            }

            .form-input, .form-select, .form-textarea {
                font-size: 16px; /* Evita zoom en iOS */
            }

            .character-grid {
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                gap: 10px;
            }

            .character-card {
                padding: 12px;
            }

            .upload-zone {
                padding: 20px 10px;
            }

            .upload-icon {
                font-size: 36px;
            }

            .file-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .file-actions {
                width: 100%;
                justify-content: space-between;
            }

            .logs-container {
                max-height: 300px;
            }

            .log-entry {
                font-size: 0.8em;
                padding: 6px 10px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1em;
            }

            .status-bar {
                font-size: 0.8em;
            }

            .status-item {
                padding: 3px 6px;
                min-width: 180px;
            }

            .btn {
                padding: 6px 10px;
                font-size: 12px;
            }

            .message {
                font-size: 13px;
                padding: 10px;
            }

            .message-btn {
                font-size: 11px;
                padding: 3px 5px;
            }

            .character-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            }

            .modal-content {
                margin: 5px;
            }
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.2);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease;
        }

        .message.user {
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-left: auto;
            color: white;
        }

        .message.ai {
            background: rgba(255, 255, 255, 0.1);
            margin-right: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .message.user {
            position: relative;
        }

        .message-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: none;
            gap: 4px;
            background: rgba(0, 0, 0, 0.8);
            padding: 4px;
            border-radius: 6px;
            backdrop-filter: blur(10px);
        }

        .message:hover .message-actions {
            display: flex;
        }

        .message-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .message-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .message-edit-form {
            margin-top: 10px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message-edit-textarea {
            width: 100%;
            min-height: 80px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            color: white;
            padding: 8px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
        }

        .message-edit-actions {
            margin-top: 8px;
            display: flex;
            gap: 8px;
        }

        .message.system {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            text-align: center;
            margin: 10px auto;
            max-width: 90%;
            font-size: 0.9em;
        }

        .input-container {
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            padding: 12px 50px 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            resize: none;
            min-height: 48px;
            max-height: 120px;
            outline: none;
            backdrop-filter: blur(10px);
        }

        .message-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .attach-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            font-size: 20px;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .attach-btn:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .attached-files {
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .attached-file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .attached-file-item:last-child {
            margin-bottom: 0;
        }

        .send-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.1);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: #1a1a2e;
            padding: 25px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.3em;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .form-select {
            background: rgba(0, 0, 0, 0.9);
            cursor: pointer;
        }

        .form-select option {
            background: #1a1a1a;
            color: white;
            padding: 8px;
        }

        .form-select option:hover {
            background: #333;
        }

        .form-select option:checked {
            background: #667eea;
        }

        input[type="range"] {
            background: transparent;
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.3);
            outline: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .model-status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .model-loaded {
            background: #28a745;
            box-shadow: 0 0 6px rgba(40, 167, 69, 0.6);
        }

        .model-available {
            background: #dc3545;
            box-shadow: 0 0 6px rgba(220, 53, 69, 0.6);
        }

        .model-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .success-message {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
            color: #51cf66;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        .error-message {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.5);
            color: #ff6b6b;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            display: inline-block;
            margin-left: 8px;
        }

        .status-indicator.offline {
            background: #dc3545;
        }

        .character-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .character-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .character-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .character-card.active {
            border-color: #667eea;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
            background: rgba(102, 126, 234, 0.2);
        }

        .character-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin: 0 auto 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .character-name {
            font-size: 0.9em;
            font-weight: 500;
            margin-bottom: 5px;
            color: white;
        }

        .character-desc {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.3;
            margin-bottom: 10px;
        }

        .character-actions {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-top: 10px;
        }

        .icon-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .icon-btn:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .character-status {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            display: none;
        }

        .character-card.active .character-status {
            display: block;
        }

        .conversation-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            margin-top: 15px;
        }

        .conversation-item {
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .conversation-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .conversation-item.active {
            background: rgba(102, 126, 234, 0.3);
            border-left: 4px solid #667eea;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-title {
            font-weight: 500;
            margin-bottom: 4px;
            color: white;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .conversation-character {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 4px;
        }

        .conversation-preview {
            font-size: 0.9em;
            color: rgba(255, 255, 255, 0.7);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-meta {
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.5);
            margin-top: 4px;
        }

        .conversation-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .conversation-item:hover .conversation-actions {
            opacity: 1;
        }

        .conversation-actions .icon-btn {
            font-size: 16px;
            padding: 8px;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .logs-container {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
        }

        .log-entry {
            padding: 8px 12px;
            margin-bottom: 8px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border-left: 3px solid;
            background: rgba(255, 255, 255, 0.05);
            animation: fadeInLog 0.3s ease;
        }

        .log-entry.success {
            border-left-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
            color: #51cf66;
        }

        .log-entry.error {
            border-left-color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
            color: #ff6b6b;
        }

        .log-entry.warning {
            border-left-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
            color: #ffd43b;
        }

        .log-entry.info {
            border-left-color: #17a2b8;
            background: rgba(23, 162, 184, 0.1);
            color: #74c0fc;
        }

        .log-entry.system {
            border-left-color: #6f42c1;
            background: rgba(111, 66, 193, 0.1);
            color: #b197fc;
        }

        .log-timestamp {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.8em;
            margin-right: 8px;
        }

        .log-content {
            display: inline;
        }

        @keyframes fadeInLog {
            from {
                opacity: 0;
                transform: translateX(-10px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .empty-logs {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }

        .file-upload-area {
            margin-bottom: 20px;
        }

        .upload-zone {
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.05);
        }

        .upload-zone:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .upload-zone.dragover {
            border-color: #51cf66;
            background: rgba(81, 207, 102, 0.1);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .upload-text {
            color: rgba(255, 255, 255, 0.9);
        }

        .uploaded-files {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
            margin-top: 15px;
        }

        .file-item {
            padding: 12px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s ease;
        }

        .file-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-info {
            flex: 1;
            min-width: 0;
        }

        .file-name {
            font-weight: 500;
            color: white;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .file-details {
            font-size: 0.85em;
            color: rgba(255, 255, 255, 0.6);
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .file-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .file-status.processing {
            background: rgba(255, 193, 7, 0.2);
            color: #ffd43b;
        }

        .file-status.completed {
            background: rgba(40, 167, 69, 0.2);
            color: #51cf66;
        }

        .file-status.error {
            background: rgba(220, 53, 69, 0.2);
            color: #ff6b6b;
        }

        .analysis-result {
            margin-top: 10px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid #667eea;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .analysis-options label {
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .analysis-options label:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .analysis-options input[type="radio"] {
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <div class="header-left">
                <h1>🚀 LM Studio Chat <span class="status-indicator offline" id="statusIndicator"></span></h1>
                <div class="status-bar" id="statusBar">
                    <div class="status-item" id="modelStatus">
                        <span class="status-label">Modelo:</span>
                        <span class="status-value" id="currentModelDisplay">No cargado</span>
                    </div>
                    <div class="status-item" id="characterStatus">
                        <span class="status-label">Personaje:</span>
                        <span class="status-value" id="currentCharacterDisplay">Ninguno</span>
                    </div>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn" id="configBtn">⚙️ Config</button>
                <div class="dropdown" id="othersDropdown">
                    <button class="btn dropdown-btn" id="othersBtn">📋 Otros ▼</button>
                    <div class="dropdown-content" id="othersMenu">
                        <button class="dropdown-item" id="chatsBtn">💬 Chats</button>
                        <button class="dropdown-item" id="charactersBtn">👤 Personajes</button>
                        <button class="dropdown-item" id="filesBtn">📁 Archivos</button>
                        <button class="dropdown-item" id="logsBtn">📋 Logs</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="chat-container" id="chatContainer">
                <div class="message system">
                    🚀 <strong>¡Bienvenido a LM Studio Chat!</strong><br><br>

                    <strong>📋 Instrucciones rápidas:</strong><br>
                    1. <strong>"⚙️ Config"</strong> → Configurar conexión<br>
                    2. <strong>"💬 Chats"</strong> → Gestionar conversaciones<br>
                    3. <strong>"👤 Personajes"</strong> → Seleccionar personalidad<br>
                    4. <strong>"📋 Logs"</strong> → Ver mensajes técnicos<br><br>

                    <em>💡 Los mensajes del sistema ahora están en el menú "📋 Logs" para mantener el chat limpio.</em>
                </div>
            </div>

            <div class="input-container">
                <div class="attached-files" id="attachedFiles" style="display: none;">
                    <!-- Archivos adjuntos aparecerán aquí -->
                </div>
                <div class="input-group">
                    <div class="input-wrapper">
                        <textarea
                            class="message-input"
                            id="messageInput"
                            placeholder="Escribe tu mensaje..."
                            rows="1"
                        ></textarea>
                        <button class="attach-btn" id="attachBtn">📎</button>
                        <input type="file" id="fileInput" style="display:none" multiple accept=".pdf,.txt,.doc,.docx,.md,.json,.csv,.xml,.html,.js,.py,.cpp,.java,.c,.h,.jpg,.jpeg,.png,.gif,.bmp,.webp,.svg">
                    </div>
                    <button class="send-btn" id="sendBtn">➤</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Configuración -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Configuración</h2>
                <button class="close-btn" id="closeSettingsBtn">&times;</button>
            </div>
            <div class="form-group">
                <label class="form-label">Servidor LM Studio</label>
                <input type="text" class="form-input" id="serverUrl" value="http://localhost:1234">
            </div>
            <div class="form-group">
                <label class="form-label">Modelo Actual</label>
                <select class="form-select" id="modelSelect">
                    <option value="">Seleccionar modelo...</option>
                </select>
                <div style="margin-top: 10px; display: flex; gap: 10px;">
                    <button class="btn" id="loadModelBtn" disabled>🚀 Cargar Modelo</button>
                    <button class="btn" id="unloadModelBtn" disabled>🗑️ Expulsar Modelo</button>
                </div>
                <div id="modelStatus" style="margin-top: 10px; font-size: 0.9em; color: rgba(255, 255, 255, 0.8);"></div>
            </div>
            <div class="form-group">
                <label class="form-label">Temperatura: <span id="temperatureValue">0.7</span></label>
                <input type="range" class="form-input" id="temperature" min="0" max="2" step="0.1" value="0.7" style="margin-top: 8px;">
            </div>
            <div class="form-group">
                <label class="form-label">Máximo de Tokens</label>
                <input type="number" class="form-input" id="maxTokens" value="2048" min="1" max="8192">
            </div>
            <div class="form-group">
                <label class="form-label">Mensaje del Sistema</label>
                <textarea class="form-textarea" id="systemMessage" placeholder="Eres un asistente útil y amigable..."></textarea>
                <div style="margin-top: 10px; display: flex; gap: 10px;">
                    <button class="btn" id="applySystemMessageBtn">✅ Aplicar Mensaje</button>
                    <button class="btn" id="clearSystemMessageBtn">🗑️ Limpiar</button>
                </div>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="testConnectionBtn">🔗 Probar Conexión</button>
                <button class="btn btn-primary" id="loadModelsBtn">🔄 Cargar Modelos</button>
            </div>
            <div id="connectionStatus"></div>
        </div>
    </div>

    <!-- Modal de Chats -->
    <div class="modal" id="chatsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Gestión de Conversaciones</h2>
                <button class="close-btn" id="closeChatsBtn">&times;</button>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="newChatBtn">+ Nueva Conversación</button>
                <button class="btn" id="importChatsBtn">📥 Importar</button>
                <button class="btn" id="exportAllChatsBtn">📤 Exportar Todo</button>
                <input type="file" id="importFileInput" style="display:none" accept=".txt,.json">
            </div>
            <div class="conversation-list" id="conversationList">
                <!-- Las conversaciones se cargarán aquí -->
            </div>
        </div>
    </div>

    <!-- Modal de Editar Conversación -->
    <div class="modal" id="editChatModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Editar Conversación</h2>
                <button class="close-btn" id="closeEditChatBtn">&times;</button>
            </div>
            <div class="form-group">
                <label class="form-label">Nombre de la Conversación</label>
                <input type="text" class="form-input" id="chatName" placeholder="Nombre de la conversación">
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="saveChatNameBtn">💾 Guardar</button>
                <button class="btn" id="cancelEditChatBtn">❌ Cancelar</button>
            </div>
        </div>
    </div>

    <!-- Modal de Personajes -->
    <div class="modal" id="charactersModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Personajes</h2>
                <button class="close-btn" id="closeCharactersBtn">&times;</button>
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="createCharacterBtn">+ Crear Personaje</button>
            </div>
            <div class="character-grid" id="characterGrid">
                <!-- Los personajes se cargarán aquí -->
            </div>
        </div>
    </div>

    <!-- Modal de Crear/Editar Personaje -->
    <div class="modal" id="characterEditModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="characterEditTitle">Crear Personaje</h2>
                <button class="close-btn" id="closeCharacterEditBtn">&times;</button>
            </div>
            <div class="form-group">
                <label class="form-label">Nombre</label>
                <input type="text" class="form-input" id="characterName" placeholder="Nombre del personaje">
            </div>
            <div class="form-group">
                <label class="form-label">Descripción</label>
                <input type="text" class="form-input" id="characterDesc" placeholder="Descripción breve">
            </div>
            <div class="form-group">
                <label class="form-label">Prompt del Sistema</label>
                <textarea class="form-textarea" id="characterPrompt" placeholder="Eres un asistente especializado en..." style="min-height: 120px;"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">Emoji/Avatar</label>
                <input type="text" class="form-input" id="characterEmoji" placeholder="🤖" maxlength="2">
            </div>
            <div class="form-group">
                <button class="btn btn-primary" id="saveCharacterBtn">💾 Guardar</button>
                <button class="btn" id="cancelCharacterBtn">❌ Cancelar</button>
            </div>
        </div>
    </div>

    <!-- Modal de Logs del Sistema -->
    <div class="modal" id="logsModal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h2 class="modal-title">📋 Logs del Sistema</h2>
                <button class="close-btn" id="closeLogsBtn">&times;</button>
            </div>
            <div class="form-group">
                <div style="display: flex; gap: 10px; margin-bottom: 15px; align-items: center;">
                    <button class="btn" id="clearLogsBtn">🗑️ Limpiar Logs</button>
                    <button class="btn" id="exportLogsBtn">📤 Exportar Logs</button>
                    <label style="margin: 0; display: flex; align-items: center; gap: 8px; color: rgba(255, 255, 255, 0.9);">
                        <input type="checkbox" id="autoScrollLogs" checked style="margin: 0;">
                        Auto-scroll
                    </label>
                    <span id="logCount" style="color: rgba(255, 255, 255, 0.7); font-size: 0.9em;">0 logs</span>
                </div>
                <div class="logs-container" id="logsContainer">
                    <div class="empty-logs">
                        <div style="text-align: center; padding: 40px; color: rgba(255, 255, 255, 0.6);">
                            <div style="font-size: 48px; margin-bottom: 16px;">📋</div>
                            <div>No hay logs del sistema</div>
                            <div style="margin-top: 8px; font-size: 0.9em;">Los mensajes técnicos aparecerán aquí</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Gestión de Archivos -->
    <div class="modal" id="filesModal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h2 class="modal-title">📁 Análisis de Archivos</h2>
                <button class="close-btn" id="closeFilesBtn">&times;</button>
            </div>
            <div class="form-group">
                <div class="file-upload-area" id="fileUploadArea">
                    <div class="upload-zone" id="uploadZone">
                        <div class="upload-icon">📄</div>
                        <div class="upload-text">
                            <strong>Arrastra archivos aquí o haz clic para seleccionar</strong>
                            <div style="margin-top: 8px; font-size: 0.9em; color: rgba(255, 255, 255, 0.7);">
                                Formatos soportados: PDF, TXT, DOC, DOCX, MD, JSON, CSV, XML, JPG, PNG, GIF, BMP, WEBP
                            </div>
                        </div>
                        <input type="file" id="fileInput" multiple accept=".pdf,.txt,.doc,.docx,.md,.json,.csv,.xml,.html,.js,.py,.cpp,.java,.c,.h,.jpg,.jpeg,.png,.gif,.bmp,.webp,.svg" style="display: none;">
                    </div>
                </div>
                <div class="analysis-options" style="margin-top: 15px;">
                    <label style="color: rgba(255, 255, 255, 0.9); margin-bottom: 8px; display: block;">Tipo de Análisis:</label>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <label style="display: flex; align-items: center; gap: 6px; color: rgba(255, 255, 255, 0.9);">
                            <input type="radio" name="analysisType" value="summary" checked>
                            📋 Resumen General
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; color: rgba(255, 255, 255, 0.9);">
                            <input type="radio" name="analysisType" value="detailed">
                            🔍 Análisis Detallado
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; color: rgba(255, 255, 255, 0.9);">
                            <input type="radio" name="analysisType" value="keywords">
                            🏷️ Palabras Clave
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; color: rgba(255, 255, 255, 0.9);">
                            <input type="radio" name="analysisType" value="questions">
                            ❓ Preguntas y Respuestas
                        </label>
                    </div>
                </div>
                <div style="margin-top: 15px; display: flex; gap: 10px; flex-wrap: wrap;">
                    <button class="btn btn-primary" id="analyzeAllBtn" disabled>🔍 Analizar Todos</button>
                    <button class="btn" id="attachToChat">📎 Adjuntar al Chat</button>
                    <button class="btn" id="clearAllBtn">🗑️ Limpiar Lista</button>
                </div>
                <div class="uploaded-files" id="uploadedFiles">
                    <!-- Los archivos subidos aparecerán aquí -->
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 JavaScript iniciando...');

        // Variables globales
        let isConnected = false;
        let models = [];
        let currentSystemMessage = '';
        let characters = [];
        let currentCharacter = null;
        let editingCharacter = null;
        let conversations = [];
        let currentConversation = null;
        let currentLoadedModel = null;
        let temperature = 0.7;
        let maxTokens = 2048;
        let systemLogs = [];
        let maxLogs = 100;
        let uploadedFiles = [];
        let fileAnalysisQueue = [];
        let attachedFiles = []; // Archivos adjuntos en el chat actual

        // Función para agregar mensajes al chat principal
        function addMessage(role, content, messageId = null) {
            const chatContainer = document.getElementById('chatContainer');
            if (!chatContainer) {
                console.error('chatContainer no encontrado');
                return;
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.dataset.messageId = messageId || Date.now().toString();
            messageDiv.dataset.originalContent = content;

            // Agregar botones de acción para mensajes de usuario y IA
            if (role === 'user' || role === 'ai') {
                const actionsHtml = role === 'user' ?
                    `<div class="message-actions">
                        <button class="message-btn" onclick="editMessage('${messageDiv.dataset.messageId}')" title="Editar">✏️</button>
                        <button class="message-btn" onclick="continueFromHere('${messageDiv.dataset.messageId}')" title="Continuar desde aquí">🔄</button>
                    </div>` :
                    `<div class="message-actions">
                        <button class="message-btn" onclick="regenerateResponse('${messageDiv.dataset.messageId}')" title="Regenerar">🔄</button>
                        <button class="message-btn" onclick="continueFromHere('${messageDiv.dataset.messageId}')" title="Continuar desde aquí">➡️</button>
                    </div>`;

                messageDiv.innerHTML = content + actionsHtml;
            } else {
                messageDiv.innerHTML = content;
            }

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            return messageDiv.dataset.messageId;
        }

        // Funciones de edición de mensajes
        function editMessage(messageId) {
            const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
            if (!messageDiv) return;

            const originalContent = messageDiv.dataset.originalContent;
            const isEditing = messageDiv.querySelector('.message-edit-form');

            if (isEditing) {
                // Cancelar edición
                cancelEdit(messageId);
                return;
            }

            // Crear formulario de edición
            const editForm = document.createElement('div');
            editForm.className = 'message-edit-form';
            editForm.innerHTML = `
                <textarea class="message-edit-textarea" placeholder="Edita tu mensaje...">${originalContent}</textarea>
                <div class="message-edit-actions">
                    <button class="btn btn-primary" onclick="saveEdit('${messageId}')">💾 Guardar</button>
                    <button class="btn" onclick="cancelEdit('${messageId}')">❌ Cancelar</button>
                </div>
            `;

            messageDiv.appendChild(editForm);

            // Enfocar el textarea
            const textarea = editForm.querySelector('.message-edit-textarea');
            textarea.focus();
            textarea.setSelectionRange(textarea.value.length, textarea.value.length);
        }

        function saveEdit(messageId) {
            const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
            if (!messageDiv) return;

            const editForm = messageDiv.querySelector('.message-edit-form');
            const textarea = editForm.querySelector('.message-edit-textarea');
            const newContent = textarea.value.trim();

            if (!newContent) {
                alert('El mensaje no puede estar vacío');
                return;
            }

            // Actualizar el contenido del mensaje
            messageDiv.dataset.originalContent = newContent;

            // Remover el formulario de edición
            editForm.remove();

            // Actualizar el HTML del mensaje
            const actionsHtml = `<div class="message-actions">
                <button class="message-btn" onclick="editMessage('${messageId}')" title="Editar">✏️</button>
                <button class="message-btn" onclick="continueFromHere('${messageId}')" title="Continuar desde aquí">🔄</button>
            </div>`;

            messageDiv.innerHTML = newContent + actionsHtml;

            addSystemLog('success', 'Mensaje editado exitosamente');

            // Si es un mensaje de usuario, ofrecer regenerar la respuesta
            if (messageDiv.classList.contains('user')) {
                if (confirm('¿Quieres que la IA responda al mensaje editado?')) {
                    regenerateFromUserMessage(messageId);
                }
            }
        }

        function cancelEdit(messageId) {
            const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
            if (!messageDiv) return;

            const editForm = messageDiv.querySelector('.message-edit-form');
            if (editForm) {
                editForm.remove();
            }
        }

        async function regenerateResponse(messageId) {
            const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
            if (!messageDiv || !messageDiv.classList.contains('ai')) return;

            // Encontrar el mensaje de usuario anterior
            const userMessage = findPreviousUserMessage(messageDiv);
            if (!userMessage) {
                addSystemLog('error', 'No se encontró el mensaje de usuario correspondiente');
                return;
            }

            // Eliminar la respuesta actual
            messageDiv.remove();

            // Regenerar la respuesta
            await regenerateFromUserMessage(userMessage.dataset.messageId);
        }

        async function regenerateFromUserMessage(userMessageId) {
            const userMessageDiv = document.querySelector(`[data-message-id="${userMessageId}"]`);
            if (!userMessageDiv) return;

            const userContent = userMessageDiv.dataset.originalContent;

            if (!isConnected) {
                addSystemLog('error', 'No hay conexión con LM Studio');
                return;
            }

            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel) {
                addSystemLog('error', 'Selecciona un modelo primero');
                return;
            }

            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const systemMsg = document.getElementById('systemMessage').value;

                const messages = [];
                if (systemMsg) {
                    messages.push({ role: 'system', content: systemMsg });
                }
                messages.push({ role: 'user', content: userContent });

                const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        messages: messages,
                        temperature: parseFloat(document.getElementById('temperature').value),
                        max_tokens: parseInt(document.getElementById('maxTokens').value)
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const aiResponse = data.choices[0].message.content;
                    addMessage('ai', aiResponse);

                    // Guardar conversación automáticamente
                    saveCurrentConversation();

                    addSystemLog('success', 'Respuesta regenerada exitosamente');
                } else {
                    throw new Error(`Error HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('❌ Error regenerando respuesta:', error);
                addSystemLog('error', 'Error regenerando respuesta');
            }
        }

        function findPreviousUserMessage(aiMessageDiv) {
            let currentElement = aiMessageDiv.previousElementSibling;

            while (currentElement) {
                if (currentElement.classList.contains('message') && currentElement.classList.contains('user')) {
                    return currentElement;
                }
                currentElement = currentElement.previousElementSibling;
            }

            return null;
        }

        function continueFromHere(messageId) {
            const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
            if (!messageDiv) return;

            // Obtener todos los mensajes hasta este punto
            const chatContainer = document.getElementById('chatContainer');
            const allMessages = Array.from(chatContainer.querySelectorAll('.message'));
            const messageIndex = allMessages.indexOf(messageDiv);

            if (messageIndex === -1) return;

            // Obtener mensajes hasta este punto (inclusive)
            const messagesUpToHere = allMessages.slice(0, messageIndex + 1);

            // Filtrar solo mensajes de usuario y IA
            const conversationMessages = messagesUpToHere.filter(msg =>
                msg.classList.contains('user') || msg.classList.contains('ai')
            );

            if (conversationMessages.length === 0) {
                addSystemLog('warning', 'No hay mensajes de conversación para continuar');
                return;
            }

            // Crear nueva conversación
            const title = prompt('Nombre para la nueva conversación:') || `Continuación ${conversations.length + 1}`;

            const newConversation = {
                id: Date.now().toString(),
                title: title,
                messages: conversationMessages.map(msg => ({
                    role: msg.classList.contains('user') ? 'user' : 'ai',
                    content: msg.dataset.originalContent,
                    timestamp: new Date().toISOString()
                })),
                characterId: currentCharacter ? currentCharacter.id : null,
                characterName: currentCharacter ? currentCharacter.name : null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            conversations.unshift(newConversation);
            currentConversation = newConversation;

            // Limpiar chat actual y cargar la nueva conversación
            chatContainer.innerHTML = '';

            // Cargar mensajes de la nueva conversación
            newConversation.messages.forEach(msg => {
                addMessage(msg.role, msg.content);
            });

            saveConversations();

            addSystemLog('success', `Nueva conversación "${title}" creada con ${conversationMessages.length} mensajes`);
        }

        // Función para agregar logs del sistema (separado del chat)
        function addSystemLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                id: Date.now() + Math.random(),
                timestamp: timestamp,
                type: type, // 'success', 'error', 'warning', 'info', 'system'
                message: message,
                fullTimestamp: new Date().toISOString()
            };

            systemLogs.unshift(logEntry); // Agregar al inicio

            // Mantener solo los últimos maxLogs
            if (systemLogs.length > maxLogs) {
                systemLogs = systemLogs.slice(0, maxLogs);
            }

            // Actualizar la vista de logs si está abierta
            updateLogsDisplay();

            // Guardar logs en localStorage
            saveSystemLogs();

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Función para actualizar la vista de logs
        function updateLogsDisplay() {
            const logsContainer = document.getElementById('logsContainer');
            const logCount = document.getElementById('logCount');

            if (!logsContainer) return;

            logCount.textContent = `${systemLogs.length} logs`;

            if (systemLogs.length === 0) {
                logsContainer.innerHTML = `
                    <div class="empty-logs">
                        <div style="text-align: center; padding: 40px; color: rgba(255, 255, 255, 0.6);">
                            <div style="font-size: 48px; margin-bottom: 16px;">📋</div>
                            <div>No hay logs del sistema</div>
                            <div style="margin-top: 8px; font-size: 0.9em;">Los mensajes técnicos aparecerán aquí</div>
                        </div>
                    </div>
                `;
                return;
            }

            logsContainer.innerHTML = '';

            systemLogs.forEach(log => {
                const logDiv = document.createElement('div');
                logDiv.className = `log-entry ${log.type}`;
                logDiv.innerHTML = `
                    <span class="log-timestamp">${log.timestamp}</span>
                    <span class="log-content">${log.message}</span>
                `;
                logsContainer.appendChild(logDiv);
            });

            // Auto-scroll si está habilitado
            if (document.getElementById('autoScrollLogs')?.checked) {
                logsContainer.scrollTop = 0; // Scroll al top porque los logs nuevos están arriba
            }
        }

        // Función para limpiar logs
        function clearSystemLogs() {
            if (confirm('¿Estás seguro de que quieres limpiar todos los logs?')) {
                systemLogs = [];
                updateLogsDisplay();
                saveSystemLogs();
                addSystemLog('info', 'Logs del sistema limpiados');
            }
        }

        // Función para exportar logs
        function exportSystemLogs() {
            if (systemLogs.length === 0) {
                alert('No hay logs para exportar');
                return;
            }

            let content = `=== LOGS DEL SISTEMA - LM STUDIO CHAT ===\n`;
            content += `Exportado: ${new Date().toLocaleString()}\n`;
            content += `Total de logs: ${systemLogs.length}\n\n`;
            content += "=".repeat(50) + "\n\n";

            // Los logs están en orden inverso (más recientes primero), así que los invertimos para exportar
            const logsToExport = [...systemLogs].reverse();

            logsToExport.forEach((log, index) => {
                content += `[${index + 1}] ${log.timestamp} [${log.type.toUpperCase()}]\n`;
                content += `${log.message}\n\n`;
            });

            downloadTextFile(`logs_sistema_${new Date().toISOString().split('T')[0]}.txt`, content);
            addSystemLog('success', `${systemLogs.length} logs exportados exitosamente`);
        }

        // Función para guardar logs en localStorage
        function saveSystemLogs() {
            try {
                localStorage.setItem('lmstudio_system_logs', JSON.stringify(systemLogs));
            } catch (error) {
                console.error('Error guardando logs:', error);
            }
        }

        // Función para cargar logs desde localStorage
        function loadSystemLogs() {
            try {
                const savedLogs = localStorage.getItem('lmstudio_system_logs');
                if (savedLogs) {
                    systemLogs = JSON.parse(savedLogs);
                    updateLogsDisplay();
                }
            } catch (error) {
                console.error('Error cargando logs:', error);
                systemLogs = [];
            }
        }

        // Funciones de análisis de archivos
        function initializeFileUpload() {
            const uploadZone = document.getElementById('uploadZone');
            const fileInput = document.getElementById('fileInput');

            // Click para seleccionar archivos
            uploadZone.addEventListener('click', () => {
                fileInput.click();
            });

            // Drag and drop
            uploadZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            });

            uploadZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
            });

            uploadZone.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
                const files = Array.from(e.dataTransfer.files);
                handleFileSelection(files);
            });

            // Input change
            fileInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                handleFileSelection(files);
                e.target.value = ''; // Limpiar input
            });
        }

        function handleFileSelection(files) {
            files.forEach(file => {
                if (isFileTypeSupported(file)) {
                    addFileToList(file);
                } else {
                    addSystemLog('warning', `Tipo de archivo no soportado: ${file.name}`);
                }
            });
        }

        function isFileTypeSupported(file) {
            const supportedTypes = [
                'text/plain',
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/markdown',
                'application/json',
                'text/csv',
                'application/xml',
                'text/xml',
                'text/html',
                'application/javascript',
                'text/javascript',
                'text/x-python',
                'text/x-c',
                'text/x-java-source',
                'image/jpeg',
                'image/jpg',
                'image/png',
                'image/gif',
                'image/bmp',
                'image/webp',
                'image/svg+xml'
            ];

            const supportedExtensions = [
                '.txt', '.pdf', '.doc', '.docx', '.md', '.json', '.csv', '.xml',
                '.html', '.htm', '.js', '.py', '.cpp', '.java', '.c', '.h',
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'
            ];

            return supportedTypes.includes(file.type) ||
                   supportedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
        }

        function addFileToList(file) {
            const fileId = Date.now() + Math.random().toString(36).substr(2, 9);
            const fileObj = {
                id: fileId,
                file: file,
                name: file.name,
                size: file.size,
                type: file.type,
                status: 'pending',
                content: null,
                analysis: null,
                uploadTime: new Date().toISOString()
            };

            uploadedFiles.push(fileObj);
            updateFilesList();

            // Leer contenido del archivo
            readFileContent(fileObj);

            addSystemLog('info', `Archivo agregado: ${file.name} (${formatFileSize(file.size)})`);
        }

        function readFileContent(fileObj) {
            const reader = new FileReader();

            reader.onload = function(e) {
                fileObj.content = e.target.result;
                fileObj.status = 'ready';
                updateFilesList();
                addSystemLog('success', `Archivo leído: ${fileObj.name}`);
            };

            reader.onerror = function() {
                fileObj.status = 'error';
                updateFilesList();
                addSystemLog('error', `Error leyendo archivo: ${fileObj.name}`);
            };

            // Determinar cómo leer el archivo según su tipo
            if (fileObj.file.type.startsWith('image/')) {
                // Para imágenes, leer como Data URL (base64)
                reader.readAsDataURL(fileObj.file);
                fileObj.isImage = true;
            } else if (fileObj.file.type.startsWith('text/') ||
                       fileObj.name.endsWith('.md') ||
                       fileObj.name.endsWith('.json') ||
                       fileObj.name.endsWith('.csv') ||
                       fileObj.name.endsWith('.xml') ||
                       fileObj.name.endsWith('.js') ||
                       fileObj.name.endsWith('.py') ||
                       fileObj.name.endsWith('.html')) {
                // Para archivos de texto, leer como texto
                reader.readAsText(fileObj.file);
                fileObj.isImage = false;
            } else {
                // Para otros tipos, intentar leer como texto
                reader.readAsText(fileObj.file);
                fileObj.isImage = false;
            }
        }

        function updateFilesList() {
            const container = document.getElementById('uploadedFiles');

            if (uploadedFiles.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: rgba(255, 255, 255, 0.6);">
                        No hay archivos cargados
                    </div>
                `;
                return;
            }

            container.innerHTML = '';

            uploadedFiles.forEach(fileObj => {
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-item';

                const statusClass = fileObj.status === 'completed' ? 'completed' :
                                  fileObj.status === 'processing' ? 'processing' :
                                  fileObj.status === 'error' ? 'error' : 'processing';

                const statusText = fileObj.status === 'completed' ? 'Analizado' :
                                 fileObj.status === 'processing' ? 'Analizando...' :
                                 fileObj.status === 'error' ? 'Error' :
                                 fileObj.status === 'ready' ? 'Listo' : 'Procesando...';

                const imagePreview = fileObj.isImage && fileObj.content ?
                    `<div style="margin-top: 8px;"><img src="${fileObj.content}" style="max-width: 100px; max-height: 60px; border-radius: 4px; object-fit: cover;" alt="Preview"></div>` : '';

                fileDiv.innerHTML = `
                    <div class="file-info">
                        <div class="file-name">
                            ${getFileIcon(fileObj.name)} ${fileObj.name}
                        </div>
                        <div class="file-details">
                            ${formatFileSize(fileObj.size)} • ${new Date(fileObj.uploadTime).toLocaleTimeString()}
                            ${fileObj.isImage ? ' • Imagen' : ''}
                        </div>
                        ${imagePreview}
                        ${fileObj.analysis ? `<div class="analysis-result">${fileObj.analysis}</div>` : ''}
                    </div>
                    <div class="file-actions">
                        <span class="file-status ${statusClass}">${statusText}</span>
                        ${fileObj.status === 'ready' ? `<button class="icon-btn" onclick="analyzeFile('${fileObj.id}')" title="Analizar">🔍</button>` : ''}
                        <button class="icon-btn" onclick="removeFile('${fileObj.id}')" title="Eliminar">🗑️</button>
                    </div>
                `;

                container.appendChild(fileDiv);
            });

            updateAnalyzeAllButton();
        }

        function getFileIcon(filename) {
            const ext = filename.toLowerCase().split('.').pop();
            const icons = {
                'pdf': '📄',
                'txt': '📝',
                'doc': '📘',
                'docx': '📘',
                'md': '📋',
                'json': '📊',
                'csv': '📈',
                'xml': '🗂️',
                'html': '🌐',
                'js': '⚡',
                'py': '🐍',
                'java': '☕',
                'cpp': '⚙️',
                'c': '⚙️',
                'jpg': '🖼️',
                'jpeg': '🖼️',
                'png': '🖼️',
                'gif': '🎞️',
                'bmp': '🖼️',
                'webp': '🖼️',
                'svg': '🎨'
            };
            return icons[ext] || '📄';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(f => f.id !== fileId);
            updateFilesList();
            addSystemLog('info', 'Archivo eliminado de la lista');
        }

        async function analyzeFile(fileId) {
            const fileObj = uploadedFiles.find(f => f.id === fileId);
            if (!fileObj) return;

            if (!isConnected) {
                addSystemLog('error', 'No hay conexión con LM Studio');
                return;
            }

            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel) {
                addSystemLog('error', 'Selecciona un modelo primero');
                return;
            }

            const analysisType = document.querySelector('input[name="analysisType"]:checked').value;

            fileObj.status = 'processing';
            updateFilesList();

            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const systemMsg = document.getElementById('systemMessage').value;

                // Crear prompt específico según el tipo de análisis
                const analysisPrompt = createAnalysisPrompt(analysisType, fileObj);

                const messages = [];
                if (systemMsg) {
                    messages.push({ role: 'system', content: systemMsg });
                }
                messages.push({ role: 'user', content: analysisPrompt });

                addSystemLog('info', `Iniciando análisis de ${fileObj.name} con tipo: ${analysisType}`);

                const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        messages: messages,
                        temperature: parseFloat(document.getElementById('temperature').value),
                        max_tokens: parseInt(document.getElementById('maxTokens').value)
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const analysis = data.choices[0].message.content;

                    fileObj.analysis = analysis;
                    fileObj.status = 'completed';
                    fileObj.analysisType = analysisType;
                    fileObj.analysisTime = new Date().toISOString();

                    updateFilesList();

                    // Agregar el análisis al chat principal también
                    addMessage('user', `📁 Análisis de archivo: ${fileObj.name}`);
                    addMessage('ai', analysis);

                    addSystemLog('success', `Análisis completado para ${fileObj.name}`);
                } else {
                    throw new Error(`Error HTTP: ${response.status}`);
                }
            } catch (error) {
                fileObj.status = 'error';
                updateFilesList();
                console.error('Error analizando archivo:', error);
                addSystemLog('error', `Error analizando ${fileObj.name}: ${error.message}`);
            }
        }

        function createAnalysisPrompt(analysisType, fileObj) {
            let basePrompt = `Por favor, analiza el siguiente archivo:\n\n` +
                           `**Nombre del archivo:** ${fileObj.name}\n` +
                           `**Tamaño:** ${formatFileSize(fileObj.size)}\n` +
                           `**Tipo:** ${fileObj.type || 'Desconocido'}\n\n`;

            if (fileObj.isImage) {
                basePrompt += `**Tipo de contenido:** Imagen\n` +
                            `**Descripción:** Por favor, analiza esta imagen que te estoy enviando. ` +
                            `Describe detalladamente lo que ves en la imagen.\n\n` +
                            `**Nota:** Aunque no puedas ver la imagen directamente, por favor proporciona ` +
                            `un análisis basado en la descripción del archivo de imagen "${fileObj.name}" ` +
                            `de ${formatFileSize(fileObj.size)}. Simula un análisis visual detallado.\n\n`;
            } else {
                basePrompt += `**Contenido del archivo:**\n\`\`\`\n${fileObj.content}\n\`\`\`\n\n`;
            }

            const isImage = fileObj.isImage;

            switch (analysisType) {
                case 'summary':
                    if (isImage) {
                        return basePrompt +
                               `**Instrucciones:** Actúa como si pudieras ver esta imagen y proporciona una descripción detallada. ` +
                               `Basándote en el nombre del archivo "${fileObj.name}" y su contexto, describe lo que probablemente ` +
                               `contiene la imagen. Incluye elementos como objetos, personas, colores, composición, ` +
                               `estilo, calidad, y cualquier texto que podría estar visible. ` +
                               `Sé creativo pero realista en tu descripción. Mantén la descripción entre 3-5 párrafos.`;
                    } else {
                        return basePrompt +
                               `**Instrucciones:** Proporciona un resumen conciso y claro del contenido del archivo. ` +
                               `Incluye los puntos principales, el propósito del documento y cualquier información relevante. ` +
                               `Mantén el resumen entre 3-5 párrafos.`;
                    }

                case 'detailed':
                    if (isImage) {
                        return basePrompt +
                               `**Instrucciones:** Realiza un análisis visual detallado y exhaustivo de la imagen. ` +
                               `Describe la composición, elementos visuales, colores, iluminación, estilo artístico, ` +
                               `calidad técnica, emociones transmitidas, y cualquier detalle significativo. ` +
                               `Si hay texto, transcríbelo. Si hay personas, describe su apariencia y expresiones.`;
                    } else {
                        return basePrompt +
                               `**Instrucciones:** Realiza un análisis detallado y exhaustivo del contenido. ` +
                               `Incluye estructura, temas principales, detalles técnicos si aplica, ` +
                               `calidad del contenido, y cualquier observación importante. ` +
                               `Sé específico y proporciona ejemplos cuando sea relevante.`;
                    }

                case 'keywords':
                    if (isImage) {
                        return basePrompt +
                               `**Instrucciones:** Identifica y lista los elementos visuales clave de la imagen. ` +
                               `Organízalos por categorías (objetos, colores, personas, lugares, emociones, estilo, etc.). ` +
                               `Incluye cualquier texto visible y explica por qué estos elementos son significativos.`;
                    } else {
                        return basePrompt +
                               `**Instrucciones:** Extrae y lista las palabras clave más importantes del documento. ` +
                               `Organízalas por categorías (temas principales, conceptos técnicos, nombres propios, etc.). ` +
                               `También proporciona una breve explicación de por qué estas palabras son relevantes.`;
                    }

                case 'questions':
                    if (isImage) {
                        return basePrompt +
                               `**Instrucciones:** Genera preguntas relevantes sobre esta imagen y respóndelas ` +
                               `basándote en lo que puedes observar. Incluye preguntas sobre qué se ve, ` +
                               `dónde podría haber sido tomada, quién aparece, qué está sucediendo, ` +
                               `cuándo podría ser, y por qué es significativa. Incluye al menos 5-8 preguntas.`;
                    } else {
                        return basePrompt +
                               `**Instrucciones:** Genera preguntas relevantes que podrían hacerse sobre este contenido ` +
                               `y proporciona las respuestas basadas en la información del archivo. ` +
                               `Incluye al menos 5-8 preguntas que cubran diferentes aspectos del documento.`;
                    }

                default:
                    if (isImage) {
                        return basePrompt + `**Instrucciones:** Analiza esta imagen y describe lo que ves de manera útil y detallada.`;
                    } else {
                        return basePrompt + `**Instrucciones:** Analiza el contenido y proporciona información útil sobre el archivo.`;
                    }
            }
        }

        async function analyzeAllFiles() {
            const readyFiles = uploadedFiles.filter(f => f.status === 'ready');

            if (readyFiles.length === 0) {
                addSystemLog('warning', 'No hay archivos listos para analizar');
                return;
            }

            if (!isConnected) {
                addSystemLog('error', 'No hay conexión con LM Studio');
                return;
            }

            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel) {
                addSystemLog('error', 'Selecciona un modelo primero');
                return;
            }

            addSystemLog('info', `Iniciando análisis de ${readyFiles.length} archivos`);

            // Analizar archivos uno por uno para evitar sobrecargar el servidor
            for (const fileObj of readyFiles) {
                await analyzeFile(fileObj.id);
                // Pequeña pausa entre análisis
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            addSystemLog('success', `Análisis completado para ${readyFiles.length} archivos`);
        }

        function clearAllFiles() {
            if (uploadedFiles.length === 0) {
                addSystemLog('info', 'No hay archivos para limpiar');
                return;
            }

            if (confirm(`¿Estás seguro de eliminar todos los ${uploadedFiles.length} archivos de la lista?`)) {
                const count = uploadedFiles.length;
                uploadedFiles = [];
                updateFilesList();
                updateAnalyzeAllButton();
                addSystemLog('info', `${count} archivos eliminados de la lista`);
            }
        }

        function updateAnalyzeAllButton() {
            const analyzeAllBtn = document.getElementById('analyzeAllBtn');
            const readyFiles = uploadedFiles.filter(f => f.status === 'ready');

            if (analyzeAllBtn) {
                analyzeAllBtn.disabled = readyFiles.length === 0;
                analyzeAllBtn.textContent = `🔍 Analizar Todos (${readyFiles.length})`;
            }
        }

        // Funciones para actualizar la barra de estado
        function updateStatusBar() {
            updateModelStatusDisplay();
            updateCharacterStatusDisplay();
        }

        function updateModelStatusDisplay() {
            const modelDisplay = document.getElementById('currentModelDisplay');
            if (currentLoadedModel) {
                modelDisplay.textContent = currentLoadedModel;
                modelDisplay.className = 'status-value active';
            } else {
                modelDisplay.textContent = 'No cargado';
                modelDisplay.className = 'status-value inactive';
            }
        }

        function updateCharacterStatusDisplay() {
            const characterDisplay = document.getElementById('currentCharacterDisplay');
            if (currentCharacter) {
                characterDisplay.textContent = `${currentCharacter.emoji} ${currentCharacter.name}`;
                characterDisplay.className = 'status-value active';
            } else {
                characterDisplay.textContent = 'Ninguno';
                characterDisplay.className = 'status-value inactive';
            }
        }

        // Funciones de modales
        function openModal(modalId) {
            console.log('🔓 Abriendo modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                console.log('✅ Modal abierto:', modalId);
                addSystemLog('info', `Modal "${modalId}" abierto`);
            } else {
                console.error('❌ Modal no encontrado:', modalId);
                alert('Error: Modal no encontrado - ' + modalId);
            }
        }

        function closeModal(modalId) {
            console.log('🔒 Cerrando modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                console.log('✅ Modal cerrado:', modalId);
            } else {
                console.error('❌ Modal no encontrado:', modalId);
            }
        }

        // Funciones del dropdown
        function toggleDropdown() {
            const dropdownContent = document.getElementById('othersMenu');
            dropdownContent.classList.toggle('show');
        }

        function closeDropdown() {
            const dropdownContent = document.getElementById('othersMenu');
            dropdownContent.classList.remove('show');
        }

        // Funciones para archivos adjuntos en el chat
        function initializeChatAttachments() {
            const attachBtn = document.getElementById('attachBtn');

            attachBtn.addEventListener('click', function() {
                // Abrir el modal de archivos directamente
                updateFilesList();
                openModal('filesModal');
            });
        }

        function handleChatFileAttachment(files) {
            files.forEach(file => {
                if (isFileTypeSupported(file)) {
                    addChatAttachment(file);
                } else {
                    addSystemLog('warning', `Tipo de archivo no soportado: ${file.name}`);
                }
            });
        }

        function addChatAttachment(file) {
            const fileId = Date.now() + Math.random().toString(36).substr(2, 9);
            const attachmentObj = {
                id: fileId,
                file: file,
                name: file.name,
                size: file.size,
                type: file.type,
                isImage: file.type.startsWith('image/'),
                content: null,
                status: 'reading'
            };

            attachedFiles.push(attachmentObj);
            updateAttachedFilesDisplay();

            // Leer contenido del archivo
            readAttachmentContent(attachmentObj);

            addSystemLog('info', `Archivo adjuntado al chat: ${file.name}`);
        }

        function readAttachmentContent(attachmentObj) {
            const reader = new FileReader();

            reader.onload = function(e) {
                attachmentObj.content = e.target.result;
                attachmentObj.status = 'ready';
                updateAttachedFilesDisplay();
                addSystemLog('success', `Archivo listo para enviar: ${attachmentObj.name}`);
            };

            reader.onerror = function() {
                attachmentObj.status = 'error';
                updateAttachedFilesDisplay();
                addSystemLog('error', `Error leyendo archivo: ${attachmentObj.name}`);
            };

            if (attachmentObj.isImage) {
                reader.readAsDataURL(attachmentObj.file);
            } else {
                reader.readAsText(attachmentObj.file);
            }
        }

        function updateAttachedFilesDisplay() {
            const container = document.getElementById('attachedFiles');

            if (attachedFiles.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            container.innerHTML = '';

            attachedFiles.forEach(attachment => {
                const attachmentDiv = document.createElement('div');
                attachmentDiv.className = 'attached-file-item';

                const preview = attachment.isImage && attachment.content ?
                    `<img src="${attachment.content}" style="width: 40px; height: 40px; border-radius: 4px; object-fit: cover;" alt="Preview">` :
                    `<div style="width: 40px; height: 40px; border-radius: 4px; background: rgba(255,255,255,0.1); display: flex; align-items: center; justify-content: center; font-size: 20px;">${getFileIcon(attachment.name)}</div>`;

                const statusText = attachment.status === 'ready' ? '✅' :
                                 attachment.status === 'reading' ? '⏳' : '❌';

                attachmentDiv.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 8px; flex: 1;">
                        ${preview}
                        <div style="flex: 1; min-width: 0;">
                            <div style="font-weight: 500; color: white; font-size: 0.9em; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${attachment.name}</div>
                            <div style="font-size: 0.8em; color: rgba(255,255,255,0.7);">${formatFileSize(attachment.size)} ${statusText}</div>
                        </div>
                    </div>
                    <button onclick="removeAttachment('${attachment.id}')" style="background: rgba(220,53,69,0.8); border: none; color: white; width: 24px; height: 24px; border-radius: 4px; cursor: pointer;" title="Eliminar">×</button>
                `;

                container.appendChild(attachmentDiv);
            });
        }

        function removeAttachment(attachmentId) {
            attachedFiles = attachedFiles.filter(a => a.id !== attachmentId);
            updateAttachedFilesDisplay();
            addSystemLog('info', 'Archivo eliminado de adjuntos');
        }

        function clearAttachments() {
            attachedFiles = [];
            updateAttachedFilesDisplay();
        }

        function attachSelectedFilesToChat() {
            const readyFiles = uploadedFiles.filter(f => f.status === 'ready');

            if (readyFiles.length === 0) {
                addSystemLog('warning', 'No hay archivos listos para adjuntar');
                return;
            }

            let attachedCount = 0;

            readyFiles.forEach(fileObj => {
                // Crear objeto de adjunto basado en el archivo de la lista
                const attachmentObj = {
                    id: Date.now() + Math.random().toString(36).substr(2, 9),
                    file: fileObj.file,
                    name: fileObj.name,
                    size: fileObj.size,
                    type: fileObj.type,
                    isImage: fileObj.isImage,
                    content: fileObj.content,
                    status: 'ready'
                };

                attachedFiles.push(attachmentObj);
                attachedCount++;
            });

            updateAttachedFilesDisplay();
            closeModal('filesModal');

            addSystemLog('success', `${attachedCount} archivos adjuntados al chat`);
        }

        // Función para probar conexión
        async function testConnection() {
            const serverUrl = document.getElementById('serverUrl').value;
            const statusDiv = document.getElementById('connectionStatus');

            try {
                statusDiv.innerHTML = '<div class="success-message">Probando conexión...</div>';
                console.log('🔗 Probando conexión a:', serverUrl);

                const response = await fetch(`${serverUrl}/v1/models`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    isConnected = true;
                    statusDiv.innerHTML = '<div class="success-message">✅ Conexión exitosa! Cargando modelos...</div>';
                    document.getElementById('statusIndicator').classList.remove('offline');
                    console.log('✅ Conexión exitosa');
                    addSystemLog('success', 'Conectado a LM Studio exitosamente');

                    // Cargar modelos automáticamente
                    await loadModels();

                    // Si hay modelos disponibles y no hay uno cargado, cargar el primero
                    if (models.length > 0 && !currentLoadedModel) {
                        const firstModel = models[0].id;
                        document.getElementById('modelSelect').value = firstModel;
                        await loadSelectedModel();
                    }

                    statusDiv.innerHTML = '<div class="success-message">✅ Conexión exitosa y modelos cargados!</div>';
                } else {
                    throw new Error(`Error HTTP: ${response.status}`);
                }
            } catch (error) {
                isConnected = false;
                statusDiv.innerHTML = '<div class="error-message">❌ Error de conexión. Verifica que LM Studio esté ejecutándose.</div>';
                document.getElementById('statusIndicator').classList.add('offline');
                console.error('❌ Error de conexión:', error);
                addSystemLog('error', 'Error de conexión con LM Studio');
            }
        }

        // Función para cargar modelos
        async function loadModels() {
            const serverUrl = document.getElementById('serverUrl').value;
            const modelSelect = document.getElementById('modelSelect');

            try {
                console.log('📥 Cargando modelos...');

                // Intentar obtener información detallada de modelos
                let response = await fetch(`${serverUrl}/api/v0/models`);
                if (!response.ok) {
                    // Fallback a API estándar
                    response = await fetch(`${serverUrl}/v1/models`);
                }

                if (response.ok) {
                    const data = await response.json();
                    models = data.data || [];

                    // Detectar modelo actualmente cargado
                    await detectLoadedModel();

                    modelSelect.innerHTML = '<option value="">Seleccionar modelo...</option>';
                    models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;

                        // Determinar estado del modelo
                        const isLoaded = currentLoadedModel === model.id;
                        const statusIcon = isLoaded ? '🟢' : '🔴';
                        const statusText = isLoaded ? ' (Cargado)' : ' (Disponible)';

                        option.textContent = `${statusIcon} ${model.id}${statusText}`;
                        option.style.color = 'white';
                        option.style.backgroundColor = isLoaded ? 'rgba(40, 167, 69, 0.2)' : 'rgba(220, 53, 69, 0.2)';

                        modelSelect.appendChild(option);
                    });

                    // Seleccionar automáticamente el modelo cargado
                    if (currentLoadedModel) {
                        modelSelect.value = currentLoadedModel;
                    }

                    updateModelButtons();
                    updateModelStatus();

                    console.log('✅ Modelos cargados:', models.length);
                    addSystemLog('success', `${models.length} modelos cargados${currentLoadedModel ? ` - Modelo activo: ${currentLoadedModel}` : ''}`);
                } else {
                    throw new Error('Error cargando modelos');
                }
            } catch (error) {
                console.error('❌ Error cargando modelos:', error);
                addSystemLog('error', 'Error cargando modelos');
            }
        }

        // Función para detectar modelo cargado
        async function detectLoadedModel() {
            const serverUrl = document.getElementById('serverUrl').value;
            try {
                // Método 1: Intentar obtener información del modelo activo desde la API v0
                try {
                    const response = await fetch(`${serverUrl}/api/v0/models/loaded`);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.model) {
                            currentLoadedModel = data.model;
                            console.log('✅ Modelo detectado via API v0:', currentLoadedModel);
                            return;
                        }
                    }
                } catch (error) {
                    console.log('API v0 no disponible para detección');
                }

                // Método 2: Intentar endpoint de estado
                try {
                    const response = await fetch(`${serverUrl}/api/status`);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.loadedModel || data.model) {
                            currentLoadedModel = data.loadedModel || data.model;
                            console.log('✅ Modelo detectado via status:', currentLoadedModel);
                            return;
                        }
                    }
                } catch (error) {
                    console.log('API status no disponible');
                }

                // Método 3: Hacer una petición de prueba para detectar el modelo activo
                try {
                    const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            model: 'auto-detect',
                            messages: [{ role: 'user', content: 'test' }],
                            max_tokens: 1
                        })
                    });

                    if (response.ok) {
                        // Si la respuesta es exitosa, el modelo está cargado
                        const data = await response.json();
                        if (data.model) {
                            currentLoadedModel = data.model;
                            console.log('✅ Modelo detectado via chat:', currentLoadedModel);
                            return;
                        }
                    } else {
                        const errorData = await response.json();
                        // Buscar información del modelo en el error
                        if (errorData.error && errorData.error.message) {
                            const message = errorData.error.message;
                            // Extraer nombre del modelo del mensaje de error
                            const modelMatch = message.match(/model[:\s]+([^\s,]+)/i) ||
                                             message.match(/loaded model[:\s]+([^\s,]+)/i) ||
                                             message.match(/current model[:\s]+([^\s,]+)/i);
                            if (modelMatch) {
                                currentLoadedModel = modelMatch[1];
                                console.log('✅ Modelo detectado via error:', currentLoadedModel);
                                return;
                            }
                        }
                    }
                } catch (error) {
                    console.log('Método de detección por chat falló');
                }

                // Método 4: Verificar cada modelo disponible
                if (models && models.length > 0) {
                    for (const model of models) {
                        try {
                            const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    model: model.id,
                                    messages: [{ role: 'user', content: 'test' }],
                                    max_tokens: 1
                                })
                            });

                            if (response.ok || response.status === 400) {
                                // 400 es normal para mensaje de prueba, significa que el modelo está cargado
                                currentLoadedModel = model.id;
                                console.log('✅ Modelo detectado via verificación:', currentLoadedModel);
                                return;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                }

                console.log('⚠️ No se pudo detectar modelo activo');
            } catch (error) {
                console.log('Error en detección de modelo:', error);
            }
        }

        // Función para actualizar botones de modelo
        function updateModelButtons() {
            const selectedModel = document.getElementById('modelSelect').value;
            const loadBtn = document.getElementById('loadModelBtn');
            const unloadBtn = document.getElementById('unloadModelBtn');

            loadBtn.disabled = !selectedModel || selectedModel === currentLoadedModel;
            unloadBtn.disabled = !currentLoadedModel;
        }

        // Función para actualizar estado del modelo
        function updateModelStatus() {
            const statusDiv = document.getElementById('modelStatus');
            if (currentLoadedModel) {
                statusDiv.innerHTML = `<span style="color: #51cf66;">🟢 Modelo cargado: ${currentLoadedModel}</span>`;
            } else {
                statusDiv.innerHTML = `<span style="color: rgba(255, 255, 255, 0.6);">⚪ Ningún modelo cargado</span>`;
            }
        }

        // Función para cargar modelo seleccionado
        async function loadSelectedModel() {
            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel) {
                alert('Selecciona un modelo primero');
                return;
            }

            const statusDiv = document.getElementById('modelStatus');
            statusDiv.innerHTML = '<span style="color: #ffc107;">⏳ Cargando modelo...</span>';

            try {
                const serverUrl = document.getElementById('serverUrl').value;

                // Hacer una petición de prueba para "cargar" el modelo
                const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: selectedModel,
                        messages: [{ role: 'user', content: 'test' }],
                        max_tokens: 1,
                        temperature: 0.1
                    })
                });

                if (response.ok || response.status === 400) {
                    // Modelo cargado exitosamente (400 es normal para mensaje de prueba)
                    currentLoadedModel = selectedModel;
                    addSystemLog('success', `Modelo "${selectedModel}" cargado exitosamente`);
                    updateStatusBar(); // Actualizar barra de estado
                    await loadModels(); // Refrescar lista
                } else {
                    throw new Error(`Error HTTP: ${response.status}`);
                }
            } catch (error) {
                statusDiv.innerHTML = '<span style="color: #dc3545;">❌ Error cargando modelo</span>';
                console.error('Error cargando modelo:', error);
                addSystemLog('error', 'Error cargando modelo. Verifica que LM Studio esté ejecutándose.');
            }
        }

        // Función para expulsar modelo actual
        async function unloadCurrentModel() {
            if (!currentLoadedModel) return;

            const statusDiv = document.getElementById('modelStatus');
            statusDiv.innerHTML = '<span style="color: #ffc107;">⏳ Expulsando modelo...</span>';

            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const modelName = currentLoadedModel;

                // Intentar usar la API de LM Studio para expulsar modelo
                let success = false;

                // Método 1: API v0 de LM Studio (más reciente)
                try {
                    const response = await fetch(`${serverUrl}/api/v0/models/unload`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            model: modelName
                        })
                    });

                    if (response.ok) {
                        success = true;
                        console.log('✅ Modelo expulsado usando API v0');
                    }
                } catch (error) {
                    console.log('API v0 no disponible, intentando método alternativo');
                }

                // Método 2: Endpoint alternativo
                if (!success) {
                    try {
                        const response = await fetch(`${serverUrl}/v1/models/${encodeURIComponent(modelName)}/unload`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            success = true;
                            console.log('✅ Modelo expulsado usando endpoint alternativo');
                        }
                    } catch (error) {
                        console.log('Endpoint alternativo no disponible');
                    }
                }

                // Método 3: Usar endpoint específico de LM Studio para eject
                if (!success) {
                    try {
                        const response = await fetch(`${serverUrl}/api/v0/models/eject`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                model: modelName
                            })
                        });

                        if (response.ok) {
                            success = true;
                            console.log('✅ Modelo expulsado usando API v0 eject');
                        }
                    } catch (error) {
                        console.log('API v0 eject no disponible');
                    }
                }

                // Método 4: Endpoint legacy de eject
                if (!success) {
                    try {
                        const response = await fetch(`${serverUrl}/api/eject`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                model: modelName
                            })
                        });

                        if (response.ok) {
                            success = true;
                            console.log('✅ Modelo expulsado usando API eject legacy');
                        }
                    } catch (error) {
                        console.log('API eject legacy no disponible');
                    }
                }

                // Método 5: Intentar con DELETE
                if (!success) {
                    try {
                        const response = await fetch(`${serverUrl}/api/v0/models/${encodeURIComponent(modelName)}`, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            success = true;
                            console.log('✅ Modelo expulsado usando DELETE');
                        }
                    } catch (error) {
                        console.log('Método DELETE no disponible');
                    }
                }

                // Verificar si el modelo fue expulsado exitosamente
                if (success) {
                    addSystemLog('info', `Expulsando modelo "${modelName}" del servidor...`);

                    // Verificar que realmente fue expulsado
                    const wasUnloaded = await verifyModelUnloaded(modelName);

                    if (wasUnloaded) {
                        currentLoadedModel = null;
                        updateStatusBar(); // Actualizar barra de estado
                        await loadModels(); // Refrescar lista para confirmar
                    } else {
                        // Si no se pudo verificar, mantener el estado pero avisar
                        addSystemLog('warning', 'No se pudo confirmar la expulsión. Revisa el estado en LM Studio.');
                    }

                } else {
                    // Si ningún método funcionó, intentar forzar la expulsión
                    console.log('Intentando método de fuerza...');

                    try {
                        // Hacer múltiples peticiones para forzar la liberación de memoria
                        const forceResponse = await fetch(`${serverUrl}/v1/chat/completions`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                model: 'invalid-model-to-force-unload',
                                messages: [{ role: 'user', content: 'force unload' }],
                                max_tokens: 1
                            })
                        });

                        // El error es esperado, pero puede forzar la liberación
                        currentLoadedModel = null;
                        addMessage('system', `⚠️ Modelo "${modelName}" expulsado (método de fuerza)`);
                        addMessage('system', '💡 Nota: Verifica en LM Studio si el modelo fue expulsado correctamente');

                        setTimeout(async () => {
                            await loadModels();
                        }, 1000);

                    } catch (error) {
                        throw new Error('No se pudo expulsar el modelo con ningún método disponible');
                    }
                }

            } catch (error) {
                statusDiv.innerHTML = '<span style="color: #dc3545;">❌ Error expulsando modelo</span>';
                console.error('Error expulsando modelo:', error);
                addSystemLog('error', `Error expulsando modelo "${currentLoadedModel}". Verifica manualmente en LM Studio.`);
            }
        }

        // Función para enviar mensaje
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            // Verificar si hay mensaje o archivos adjuntos
            if (!message && attachedFiles.length === 0) return;

            if (!isConnected) {
                addSystemLog('error', 'No hay conexión con LM Studio');
                return;
            }

            const selectedModel = document.getElementById('modelSelect').value;
            if (!selectedModel) {
                addSystemLog('warning', 'Selecciona un modelo primero');
                return;
            }

            // Verificar que todos los archivos adjuntos estén listos
            const pendingFiles = attachedFiles.filter(f => f.status !== 'ready');
            if (pendingFiles.length > 0) {
                addSystemLog('warning', 'Espera a que todos los archivos estén listos');
                return;
            }

            // Crear nueva conversación si no existe
            if (!currentConversation) {
                const title = `Chat ${conversations.length + 1}`;
                currentConversation = {
                    id: Date.now().toString(),
                    title: title,
                    messages: [],
                    characterId: currentCharacter ? currentCharacter.id : null,
                    characterName: currentCharacter ? currentCharacter.name : null,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                conversations.unshift(currentConversation);
                saveConversations();
            }

            // Construir mensaje completo con archivos adjuntos
            let fullMessage = message;

            if (attachedFiles.length > 0) {
                fullMessage += '\n\n**Archivos adjuntos:**\n';

                attachedFiles.forEach((file, index) => {
                    fullMessage += `\n**${index + 1}. ${file.name}** (${formatFileSize(file.size)})\n`;

                    if (file.isImage) {
                        fullMessage += `Tipo: Imagen\n`;
                        fullMessage += `Descripción: Por favor analiza esta imagen. `;
                        fullMessage += `Es un archivo de imagen llamado "${file.name}" de ${formatFileSize(file.size)}. `;
                        fullMessage += `Proporciona una descripción detallada de lo que probablemente contiene esta imagen.\n`;
                    } else {
                        fullMessage += `Contenido:\n\`\`\`\n${file.content}\n\`\`\`\n`;
                    }
                });
            }

            // Agregar mensaje del usuario (mostrar solo el texto, no los archivos)
            const displayMessage = message + (attachedFiles.length > 0 ? ` 📎 ${attachedFiles.length} archivo(s)` : '');
            addMessage('user', displayMessage);

            input.value = '';
            clearAttachments(); // Limpiar archivos adjuntos después de enviar

            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const systemMsg = document.getElementById('systemMessage').value;

                const messages = [];
                if (systemMsg) {
                    messages.push({ role: 'system', content: systemMsg });
                }
                messages.push({ role: 'user', content: fullMessage });

                const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        messages: messages,
                        temperature: parseFloat(document.getElementById('temperature').value),
                        max_tokens: parseInt(document.getElementById('maxTokens').value)
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const aiResponse = data.choices[0].message.content;
                    addMessage('ai', aiResponse);

                    // Guardar conversación automáticamente
                    saveCurrentConversation();
                } else {
                    throw new Error(`Error HTTP: ${response.status}`);
                }
            } catch (error) {
                console.error('❌ Error enviando mensaje:', error);
                addSystemLog('error', 'Error enviando mensaje');
            }
        }

        // Función para verificar si un modelo específico está cargado
        async function isModelLoaded(modelId) {
            const serverUrl = document.getElementById('serverUrl').value;
            try {
                const response = await fetch(`${serverUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: modelId,
                        messages: [{ role: 'user', content: 'test' }],
                        max_tokens: 1
                    })
                });

                // Si responde OK o 400 (error de contenido pero modelo cargado), está cargado
                return response.ok || response.status === 400;
            } catch (error) {
                return false;
            }
        }

        // Función mejorada para verificar estado después de expulsión
        async function verifyModelUnloaded(modelId) {
            console.log(`🔍 Verificando si ${modelId} fue expulsado...`);

            // Esperar un momento para que el servidor procese
            await new Promise(resolve => setTimeout(resolve, 2000));

            const isLoaded = await isModelLoaded(modelId);

            if (!isLoaded) {
                addSystemLog('success', `Confirmado: Modelo "${modelId}" expulsado exitosamente`);
                return true;
            } else {
                addSystemLog('warning', `El modelo "${modelId}" parece seguir cargado. Verifica manualmente en LM Studio.`);
                return false;
            }
        }

        // Funciones de persistencia
        function saveSettings() {
            const settings = {
                serverUrl: document.getElementById('serverUrl').value,
                systemMessage: document.getElementById('systemMessage').value,
                currentCharacterId: currentCharacter ? currentCharacter.id : null,
                temperature: parseFloat(document.getElementById('temperature').value),
                maxTokens: parseInt(document.getElementById('maxTokens').value),
                currentLoadedModel: currentLoadedModel,
                isConnected: isConnected
            };
            localStorage.setItem('lmstudio_settings', JSON.stringify(settings));
        }

        function loadSettings() {
            const savedSettings = localStorage.getItem('lmstudio_settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                document.getElementById('serverUrl').value = settings.serverUrl || 'http://localhost:1234';
                document.getElementById('systemMessage').value = settings.systemMessage || '';
                document.getElementById('temperature').value = settings.temperature || 0.7;
                document.getElementById('maxTokens').value = settings.maxTokens || 2048;
                document.getElementById('temperatureValue').textContent = settings.temperature || 0.7;

                currentSystemMessage = settings.systemMessage || '';
                temperature = settings.temperature || 0.7;
                maxTokens = settings.maxTokens || 2048;
                currentLoadedModel = settings.currentLoadedModel || null;

                return settings;
            }
            return null;
        }

        // Funciones de mensaje del sistema
        function applySystemMessage() {
            const systemMsg = document.getElementById('systemMessage').value.trim();
            currentSystemMessage = systemMsg;

            if (systemMsg) {
                addSystemLog('success', `Mensaje del sistema aplicado: "${systemMsg.substring(0, 50)}${systemMsg.length > 50 ? '...' : ''}"`);
            } else {
                addSystemLog('success', 'Mensaje del sistema limpiado');
            }

            saveSettings();
        }

        function clearSystemMessage() {
            document.getElementById('systemMessage').value = '';
            currentSystemMessage = '';
            addSystemLog('success', 'Mensaje del sistema limpiado');
            saveSettings();
        }

        // Funciones de conversaciones
        function loadConversations() {
            const savedConversations = localStorage.getItem('lmstudio_conversations');
            if (savedConversations) {
                conversations = JSON.parse(savedConversations);
            }
            updateConversationsList();
        }

        function saveConversations() {
            localStorage.setItem('lmstudio_conversations', JSON.stringify(conversations));
        }

        function createNewConversation() {
            const title = prompt('Nombre de la nueva conversación:') || `Chat ${conversations.length + 1}`;

            const newConversation = {
                id: Date.now().toString(),
                title: title,
                messages: [],
                characterId: currentCharacter ? currentCharacter.id : null,
                characterName: currentCharacter ? currentCharacter.name : null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            conversations.unshift(newConversation);
            currentConversation = newConversation;

            // Limpiar chat actual
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = '';
            addSystemLog('success', `Nueva conversación "${title}" creada`);

            saveConversations();
            updateConversationsList();
            closeModal('chatsModal');
        }

        function loadConversation(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (!conversation) return;

            // Guardar conversación actual antes de cambiar
            if (currentConversation) {
                saveCurrentConversation();
            }

            currentConversation = conversation;

            // Limpiar chat
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = '';

            // Cargar personaje de la conversación
            if (conversation.characterId) {
                const character = characters.find(c => c.id === conversation.characterId);
                if (character) {
                    currentCharacter = character;
                    document.getElementById('systemMessage').value = character.prompt;
                    currentSystemMessage = character.prompt;
                    updateCharacterGrid();
                }
            }

            // Cargar mensajes
            if (conversation.messages && conversation.messages.length > 0) {
                conversation.messages.forEach(msg => {
                    addMessage(msg.role, msg.content);
                });
            } else {
                addMessage('system', `📖 Conversación "${conversation.title}" cargada`);
            }

            updateConversationsList();
            closeModal('chatsModal');
        }

        function saveCurrentConversation() {
            if (!currentConversation) return;

            // Obtener mensajes del chat actual
            const chatContainer = document.getElementById('chatContainer');
            const messageElements = chatContainer.querySelectorAll('.message:not(.system)');

            currentConversation.messages = Array.from(messageElements).map(el => ({
                role: el.classList.contains('user') ? 'user' : 'ai',
                content: el.textContent || el.innerHTML,
                timestamp: new Date().toISOString()
            }));

            currentConversation.updatedAt = new Date().toISOString();
            currentConversation.characterId = currentCharacter ? currentCharacter.id : null;
            currentConversation.characterName = currentCharacter ? currentCharacter.name : null;

            saveConversations();
        }

        function updateConversationsList() {
            const list = document.getElementById('conversationList');

            if (conversations.length === 0) {
                list.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">💬</div>
                        <div>No hay conversaciones</div>
                        <div style="margin-top: 8px; font-size: 0.9em;">Crea una nueva conversación para empezar</div>
                    </div>
                `;
                return;
            }

            list.innerHTML = '';

            conversations.forEach(conversation => {
                const item = document.createElement('div');
                item.className = `conversation-item ${currentConversation && currentConversation.id === conversation.id ? 'active' : ''}`;

                const lastMessage = conversation.messages && conversation.messages.length > 0
                    ? conversation.messages[conversation.messages.length - 1].content
                    : 'Sin mensajes';

                const characterInfo = conversation.characterName
                    ? `👤 ${conversation.characterName}`
                    : '👤 Sin personaje';

                const messageCount = conversation.messages ? conversation.messages.length : 0;
                const date = new Date(conversation.updatedAt).toLocaleDateString();

                item.innerHTML = `
                    <div class="conversation-info">
                        <div class="conversation-title">
                            💬 ${conversation.title}
                        </div>
                        <div class="conversation-character">${characterInfo}</div>
                        <div class="conversation-preview">${lastMessage.substring(0, 60)}${lastMessage.length > 60 ? '...' : ''}</div>
                        <div class="conversation-meta">${messageCount} mensajes • ${date}</div>
                    </div>
                    <div class="conversation-actions">
                        <button class="icon-btn" onclick="loadConversation('${conversation.id}')" title="Abrir">📂</button>
                        <button class="icon-btn" onclick="editConversationName('${conversation.id}')" title="Editar">✏️</button>
                        <button class="icon-btn" onclick="exportConversation('${conversation.id}')" title="Exportar">📤</button>
                        <button class="icon-btn" onclick="deleteConversation('${conversation.id}')" title="Eliminar">🗑️</button>
                    </div>
                `;

                list.appendChild(item);
            });
        }

        let editingConversationId = null;

        function editConversationName(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (conversation) {
                editingConversationId = conversationId;
                document.getElementById('chatName').value = conversation.title;
                openModal('editChatModal');
            }
        }

        function saveChatName() {
            if (!editingConversationId) return;

            const newName = document.getElementById('chatName').value.trim();
            if (!newName) {
                alert('El nombre no puede estar vacío');
                return;
            }

            const conversation = conversations.find(c => c.id === editingConversationId);
            if (conversation) {
                conversation.title = newName;
                conversation.updatedAt = new Date().toISOString();
                saveConversations();
                updateConversationsList();
                addSystemLog('success', `Conversación renombrada a "${newName}"`);
            }

            closeModal('editChatModal');
        }

        function deleteConversation(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (!conversation) return;

            if (confirm(`¿Estás seguro de eliminar la conversación "${conversation.title}"?`)) {
                conversations = conversations.filter(c => c.id !== conversationId);

                if (currentConversation && currentConversation.id === conversationId) {
                    currentConversation = null;
                    const chatContainer = document.getElementById('chatContainer');
                    chatContainer.innerHTML = '';
                    addSystemLog('success', 'Conversación eliminada');
                }

                saveConversations();
                updateConversationsList();
            }
        }

        function exportConversation(conversationId) {
            const conversation = conversations.find(c => c.id === conversationId);
            if (!conversation) return;

            const exportData = {
                title: conversation.title,
                character: conversation.characterName || 'Sin personaje',
                createdAt: conversation.createdAt,
                updatedAt: conversation.updatedAt,
                messages: conversation.messages || []
            };

            let content = `=== ${exportData.title} ===\n`;
            content += `Personaje: ${exportData.character}\n`;
            content += `Creado: ${new Date(exportData.createdAt).toLocaleString()}\n`;
            content += `Actualizado: ${new Date(exportData.updatedAt).toLocaleString()}\n`;
            content += `Mensajes: ${exportData.messages.length}\n\n`;
            content += "=".repeat(50) + "\n\n";

            exportData.messages.forEach((msg, index) => {
                const role = msg.role === 'user' ? 'Usuario' : 'IA';
                content += `[${index + 1}] ${role}:\n${msg.content}\n\n`;
            });

            downloadTextFile(`${conversation.title}.txt`, content);
            addSystemLog('success', `Conversación "${conversation.title}" exportada`);
        }

        function exportAllConversations() {
            if (conversations.length === 0) {
                alert('No hay conversaciones para exportar');
                return;
            }

            const exportData = {
                exportDate: new Date().toISOString(),
                totalConversations: conversations.length,
                conversations: conversations
            };

            downloadTextFile('todas_las_conversaciones.json', JSON.stringify(exportData, null, 2));
            addSystemLog('success', `${conversations.length} conversaciones exportadas`);
        }

        function importConversations() {
            document.getElementById('importFileInput').click();
        }

        function handleImportFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const content = e.target.result;
                    let importedData;

                    if (file.name.endsWith('.json')) {
                        importedData = JSON.parse(content);

                        if (importedData.conversations && Array.isArray(importedData.conversations)) {
                            const importCount = importedData.conversations.length;

                            // Agregar conversaciones importadas
                            importedData.conversations.forEach(conv => {
                                conv.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
                                conv.title = conv.title + ' (Importado)';
                            });

                            conversations = [...importedData.conversations, ...conversations];
                            saveConversations();
                            updateConversationsList();
                            addSystemLog('success', `${importCount} conversaciones importadas`);
                        } else {
                            throw new Error('Formato de archivo no válido');
                        }
                    } else {
                        alert('Solo se admiten archivos .json para importar');
                    }
                } catch (error) {
                    alert('Error al importar archivo: ' + error.message);
                }
            };

            reader.readAsText(file);
            event.target.value = ''; // Limpiar input
        }

        function downloadTextFile(filename, content) {
            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Funciones de personajes
        function loadCharacters() {
            // Cargar personajes desde localStorage
            const savedCharacters = localStorage.getItem('lmstudio_characters');
            if (savedCharacters) {
                characters = JSON.parse(savedCharacters);
            } else {
                // Crear personajes por defecto
                characters = [
                    {
                        id: 'default',
                        name: 'Asistente General',
                        description: 'Asistente útil y amigable',
                        prompt: 'Eres un asistente útil y amigable. Responde de manera clara y concisa.',
                        emoji: '🤖'
                    },
                    {
                        id: 'teacher',
                        name: 'Profesor',
                        description: 'Educador paciente y didáctico',
                        prompt: 'Eres un profesor experimentado y paciente. Explicas conceptos de manera clara, usas ejemplos y te aseguras de que el estudiante comprenda antes de continuar.',
                        emoji: '👨‍🏫'
                    },
                    {
                        id: 'programmer',
                        name: 'Programador',
                        description: 'Experto en desarrollo de software',
                        prompt: 'Eres un programador senior con amplia experiencia. Ayudas con código, debugging, mejores prácticas y arquitectura de software. Siempre proporcionas ejemplos de código cuando es relevante.',
                        emoji: '👨‍💻'
                    },
                    {
                        id: 'creative',
                        name: 'Creativo',
                        description: 'Asistente creativo e imaginativo',
                        prompt: 'Eres un asistente creativo e imaginativo. Te especializas en escritura creativa, brainstorming, ideas innovadoras y soluciones fuera de lo convencional.',
                        emoji: '🎨'
                    }
                ];
                saveCharacters();
            }
            updateCharacterGrid();
        }

        function saveCharacters() {
            localStorage.setItem('lmstudio_characters', JSON.stringify(characters));
        }

        function updateCharacterGrid() {
            const grid = document.getElementById('characterGrid');
            grid.innerHTML = '';

            characters.forEach(character => {
                const card = document.createElement('div');
                card.className = `character-card ${currentCharacter && currentCharacter.id === character.id ? 'active' : ''}`;

                card.innerHTML = `
                    <div class="character-status"></div>
                    <div class="character-avatar">${character.emoji}</div>
                    <div class="character-name">${character.name}</div>
                    <div class="character-desc">${character.description}</div>
                    <div class="character-actions">
                        <button class="icon-btn" onclick="selectCharacter('${character.id}')" title="Seleccionar">✅</button>
                        <button class="icon-btn" onclick="editCharacter('${character.id}')" title="Editar">✏️</button>
                        ${character.id !== 'default' ? `<button class="icon-btn" onclick="deleteCharacter('${character.id}')" title="Eliminar">🗑️</button>` : ''}
                    </div>
                `;

                grid.appendChild(card);
            });
        }

        function selectCharacter(characterId) {
            const character = characters.find(c => c.id === characterId);
            if (character) {
                currentCharacter = character;

                // Cargar automáticamente el prompt en el mensaje del sistema
                document.getElementById('systemMessage').value = character.prompt;
                currentSystemMessage = character.prompt;

                updateCharacterGrid();
                addSystemLog('success', `Personaje "${character.name}" seleccionado y cargado en mensaje del sistema`);

                // Actualizar barra de estado
                updateStatusBar();

                // Guardar configuración
                saveSettings();

                console.log('✅ Personaje seleccionado:', character.name);
            }
        }

        function createCharacter() {
            editingCharacter = null;
            document.getElementById('characterEditTitle').textContent = 'Crear Personaje';
            document.getElementById('characterName').value = '';
            document.getElementById('characterDesc').value = '';
            document.getElementById('characterPrompt').value = '';
            document.getElementById('characterEmoji').value = '🤖';
            openModal('characterEditModal');
        }

        function editCharacter(characterId) {
            const character = characters.find(c => c.id === characterId);
            if (character) {
                editingCharacter = character;
                document.getElementById('characterEditTitle').textContent = 'Editar Personaje';
                document.getElementById('characterName').value = character.name;
                document.getElementById('characterDesc').value = character.description;
                document.getElementById('characterPrompt').value = character.prompt;
                document.getElementById('characterEmoji').value = character.emoji;
                openModal('characterEditModal');
            }
        }

        function saveCharacter() {
            const name = document.getElementById('characterName').value.trim();
            const description = document.getElementById('characterDesc').value.trim();
            const prompt = document.getElementById('characterPrompt').value.trim();
            const emoji = document.getElementById('characterEmoji').value.trim();

            if (!name || !prompt) {
                alert('El nombre y el prompt son obligatorios');
                return;
            }

            if (editingCharacter) {
                // Editar personaje existente
                editingCharacter.name = name;
                editingCharacter.description = description;
                editingCharacter.prompt = prompt;
                editingCharacter.emoji = emoji;
                addSystemLog('success', `Personaje "${name}" actualizado`);
            } else {
                // Crear nuevo personaje
                const newCharacter = {
                    id: Date.now().toString(),
                    name: name,
                    description: description,
                    prompt: prompt,
                    emoji: emoji
                };
                characters.push(newCharacter);
                addSystemLog('success', `Personaje "${name}" creado`);
            }

            saveCharacters();
            updateCharacterGrid();
            closeModal('characterEditModal');
        }

        function deleteCharacter(characterId) {
            if (characterId === 'default') {
                alert('No se puede eliminar el personaje por defecto');
                return;
            }

            const character = characters.find(c => c.id === characterId);
            if (character && confirm(`¿Estás seguro de eliminar el personaje "${character.name}"?`)) {
                characters = characters.filter(c => c.id !== characterId);

                if (currentCharacter && currentCharacter.id === characterId) {
                    currentCharacter = characters[0];
                    selectCharacter(currentCharacter.id);
                }

                saveCharacters();
                updateCharacterGrid();
                addSystemLog('success', `Personaje "${character.name}" eliminado`);
            }
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 DOM cargado, configurando event listeners...');

            // Botones del header
            document.getElementById('configBtn').addEventListener('click', function() {
                console.log('🔧 Botón Config clickeado');
                openModal('settingsModal');
            });

            // Event listener para el dropdown "Otros"
            document.getElementById('othersBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                toggleDropdown();
            });

            // Cerrar dropdown al hacer clic fuera
            document.addEventListener('click', function(event) {
                const dropdown = document.getElementById('othersDropdown');
                if (!dropdown.contains(event.target)) {
                    closeDropdown();
                }
            });

            document.getElementById('chatsBtn').addEventListener('click', function() {
                console.log('💬 Botón Chats clickeado');
                openModal('chatsModal');
            });

            document.getElementById('charactersBtn').addEventListener('click', function() {
                console.log('👤 Botón Personajes clickeado');
                openModal('charactersModal');
            });

            document.getElementById('filesBtn').addEventListener('click', function() {
                console.log('📁 Botón Archivos clickeado');
                updateFilesList(); // Actualizar antes de mostrar
                openModal('filesModal');
            });

            document.getElementById('logsBtn').addEventListener('click', function() {
                console.log('📋 Botón Logs clickeado');
                updateLogsDisplay(); // Actualizar antes de mostrar
                openModal('logsModal');
            });

            // Botones de cerrar modales
            document.getElementById('closeSettingsBtn').addEventListener('click', function() {
                closeModal('settingsModal');
            });

            document.getElementById('closeChatsBtn').addEventListener('click', function() {
                closeModal('chatsModal');
            });

            document.getElementById('closeEditChatBtn').addEventListener('click', function() {
                closeModal('editChatModal');
            });

            // Botones de gestión de chats
            document.getElementById('newChatBtn').addEventListener('click', createNewConversation);
            document.getElementById('importChatsBtn').addEventListener('click', importConversations);
            document.getElementById('exportAllChatsBtn').addEventListener('click', exportAllConversations);
            document.getElementById('saveChatNameBtn').addEventListener('click', saveChatName);
            document.getElementById('cancelEditChatBtn').addEventListener('click', function() {
                closeModal('editChatModal');
            });

            // Input de importar archivos
            document.getElementById('importFileInput').addEventListener('change', handleImportFile);

            // Botones de logs
            document.getElementById('clearLogsBtn').addEventListener('click', clearSystemLogs);
            document.getElementById('exportLogsBtn').addEventListener('click', exportSystemLogs);

            // Botones de archivos
            document.getElementById('analyzeAllBtn').addEventListener('click', analyzeAllFiles);
            document.getElementById('attachToChat').addEventListener('click', attachSelectedFilesToChat);
            document.getElementById('clearAllBtn').addEventListener('click', clearAllFiles);

            document.getElementById('closeCharactersBtn').addEventListener('click', function() {
                closeModal('charactersModal');
            });

            document.getElementById('closeCharacterEditBtn').addEventListener('click', function() {
                closeModal('characterEditModal');
            });

            document.getElementById('closeFilesBtn').addEventListener('click', function() {
                closeModal('filesModal');
            });

            document.getElementById('closeLogsBtn').addEventListener('click', function() {
                closeModal('logsModal');
            });

            // Botones de personajes
            document.getElementById('createCharacterBtn').addEventListener('click', createCharacter);
            document.getElementById('saveCharacterBtn').addEventListener('click', saveCharacter);
            document.getElementById('cancelCharacterBtn').addEventListener('click', function() {
                closeModal('characterEditModal');
            });

            // Botones de configuración
            document.getElementById('testConnectionBtn').addEventListener('click', testConnection);
            document.getElementById('loadModelsBtn').addEventListener('click', loadModels);
            document.getElementById('applySystemMessageBtn').addEventListener('click', applySystemMessage);
            document.getElementById('clearSystemMessageBtn').addEventListener('click', clearSystemMessage);

            // Botones de gestión de modelos
            document.getElementById('loadModelBtn').addEventListener('click', loadSelectedModel);
            document.getElementById('unloadModelBtn').addEventListener('click', unloadCurrentModel);

            // Control de temperatura
            document.getElementById('temperature').addEventListener('input', function() {
                document.getElementById('temperatureValue').textContent = this.value;
                saveSettings();
            });

            // Control de modelo seleccionado
            document.getElementById('modelSelect').addEventListener('change', function() {
                updateModelButtons();
                saveSettings();
            });

            // Botón de enviar
            document.getElementById('sendBtn').addEventListener('click', sendMessage);

            // Enter en el input
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Botón de adjuntar
            document.getElementById('attachBtn').addEventListener('click', function() {
                document.getElementById('fileInput').click();
            });

            // Cerrar modales al hacer clic fuera
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal')) {
                    e.target.classList.remove('active');
                }
            });

            // Guardar configuración automáticamente
            document.getElementById('serverUrl').addEventListener('change', saveSettings);
            document.getElementById('systemMessage').addEventListener('change', saveSettings);
            document.getElementById('maxTokens').addEventListener('change', saveSettings);

            // Guardar conversación antes de cerrar la ventana
            window.addEventListener('beforeunload', function() {
                if (currentConversation) {
                    saveCurrentConversation();
                }
                saveSettings();
            });

            // Cargar configuraciones guardadas
            const settings = loadSettings();

            // Cargar logs del sistema
            loadSystemLogs();

            // Inicializar personajes
            loadCharacters();

            // Cargar conversaciones
            loadConversations();

            // Cargar personaje guardado
            if (settings && settings.currentCharacterId) {
                const character = characters.find(c => c.id === settings.currentCharacterId);
                if (character) {
                    selectCharacter(settings.currentCharacterId);
                }
            }

            // Crear conversación inicial si no hay ninguna
            if (conversations.length === 0) {
                currentConversation = {
                    id: Date.now().toString(),
                    title: 'Chat Principal',
                    messages: [],
                    characterId: currentCharacter ? currentCharacter.id : null,
                    characterName: currentCharacter ? currentCharacter.name : null,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                conversations.push(currentConversation);
                saveConversations();
            } else {
                // Cargar la conversación más reciente
                currentConversation = conversations[0];
            }

            // Actualizar barra de estado inicial
            updateStatusBar();

            // Inicializar sistema de archivos
            initializeFileUpload();

            // Inicializar archivos adjuntos en chat
            initializeChatAttachments();

            console.log('✅ Event listeners configurados');
            addSystemLog('success', 'Aplicación iniciada correctamente');
            addSystemLog('info', `${characters.length} personajes disponibles`);
            addSystemLog('info', 'Sistema de análisis de archivos listo');

            // Test automático
            setTimeout(() => {
                console.log('🧪 Test automático: Verificando funcionalidad...');
                addSystemLog('system', 'Test: JavaScript funcionando correctamente');
            }, 1000);
        });

        console.log('✅ JavaScript cargado completamente');
    </script>
</body>
</html>
