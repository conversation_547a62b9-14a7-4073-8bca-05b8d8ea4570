# LM Studio Chat Mobile

Una aplicación móvil React Native para interactuar con LM Studio, con soporte completo para modelos de visión y análisis de archivos.

## 🚀 Características

### ✨ Funcionalidades Principales
- **Chat con IA** - Conversaciones fluidas con modelos de LM Studio
- **Soporte de Visión** - Análisis de imágenes con modelos compatibles (Gemma, LLaVA, etc.)
- **Archivos Adjuntos** - Sube documentos e imágenes desde cámara, galería o archivos
- **Gestión de Conversaciones** - Crea, edita y organiza tus chats
- **Personajes de IA** - Selecciona diferentes personalidades
- **Logs del Sistema** - Monitoreo técnico y debugging

### 🎨 Interfaz Adaptable
- **Tamaño de Interfaz** - Slider para ajustar elementos UI (70% - 150%)
- **Tamaño de Texto** - Slider para ajustar fuentes (80% - 180%)
- **Diseño Responsivo** - Optimizado para diferentes tamaños de pantalla
- **Tema Oscu<PERSON>** - Interfaz moderna y cómoda para los ojos

### 📱 Optimización Móvil
- **Navegación por Pestañas** - Chat, Configuración, Otros
- **Gestos Táctiles** - Drag & drop, swipe, tap optimizados
- **Teclado Inteligente** - Ajuste automático de layout
- **Rendimiento** - Carga rápida y uso eficiente de memoria

## 📋 Requisitos

### Software Necesario
- **Node.js** 16.0 o superior
- **Expo CLI** (`npm install -g expo-cli`)
- **Expo Go** app en tu dispositivo móvil
- **LM Studio** ejecutándose en tu PC/servidor

### Dispositivos Compatibles
- **Android** 6.0+ (API 23+)
- **iOS** 11.0+
- **Conexión WiFi** en la misma red que LM Studio

## 🛠️ Instalación

### 1. Clonar y Configurar
```bash
# Clonar el proyecto
git clone <repository-url>
cd lm-studio-chat-mobile

# Instalar dependencias
npm install

# Iniciar el servidor de desarrollo
expo start
```

### 2. Configurar LM Studio
1. **Abrir LM Studio** en tu PC
2. **Cargar un modelo** (recomendado: Gemma-3-4b-it para visión)
3. **Iniciar servidor** en el puerto 1234
4. **Obtener IP** de tu PC (ej: *************)

### 3. Conectar la App
1. **Escanear QR** con Expo Go
2. **Ir a Configuración** en la app
3. **Configurar URL** del servidor (ej: http://*************:1234)
4. **Probar Conexión**
5. **Cargar Modelos**

## 📖 Guía de Uso

### 🔧 Configuración Inicial
1. **Configuración → URL del Servidor**
   - Ingresa la IP de tu PC con LM Studio
   - Formato: `http://*************:1234`
   - Prueba la conexión

2. **Cargar Modelos**
   - Haz clic en "Cargar Modelos"
   - Selecciona un modelo de la lista
   - Los modelos con 👁️ soportan visión

3. **Ajustar Interfaz**
   - Tamaño de Interfaz: 70% - 150%
   - Tamaño de Texto: 80% - 180%
   - Los cambios se aplican inmediatamente

### 💬 Chatear con IA
1. **Ir a la pestaña Chat**
2. **Escribir mensaje** en el campo de texto
3. **Adjuntar archivos** con el botón 📎
4. **Enviar** y recibir respuesta

### 📎 Adjuntar Archivos
1. **Toca el botón 📎** en el chat
2. **Selecciona origen:**
   - 📷 Cámara - Tomar foto
   - 🖼️ Galería - Seleccionar imágenes
   - 📄 Documentos - Archivos de texto

3. **Formatos soportados:**
   - **Imágenes:** JPG, PNG, GIF, BMP, WEBP
   - **Documentos:** PDF, TXT, DOC, DOCX, MD
   - **Datos:** JSON, CSV, XML
   - **Código:** HTML, JS, PY, JAVA, CPP

### 👤 Gestión de Personajes
1. **Otros → Gestión de Personajes**
2. **Crear nuevo** personaje con prompt personalizado
3. **Seleccionar** personaje activo
4. **El prompt se aplica** automáticamente

### 💬 Gestión de Conversaciones
1. **Otros → Gestión de Chats**
2. **Crear nueva** conversación
3. **Renombrar** conversaciones existentes
4. **Exportar/Importar** chats como archivos .txt

## 🔍 Modelos de Visión

### Modelos Compatibles
- **Gemma-3-4b-it** ✅ (Recomendado)
- **LLaVA** ✅
- **BakLLaVA** ✅
- **Moondream** ✅
- **CogVLM** ✅
- **Qwen-VL** ✅
- **Phi-3-Vision** ✅

### Análisis de Imágenes
Los modelos con visión pueden:
- **Describir** contenido de imágenes
- **Identificar** objetos y personas
- **Leer texto** en imágenes
- **Analizar** composición y colores
- **Responder preguntas** sobre imágenes

## 🐛 Solución de Problemas

### Conexión
- **Error de conexión:** Verifica que LM Studio esté ejecutándose
- **IP incorrecta:** Usa `ipconfig` (Windows) o `ifconfig` (Mac/Linux)
- **Firewall:** Permite conexiones en puerto 1234
- **Red:** Asegúrate de estar en la misma WiFi

### Archivos
- **No se adjunta:** Verifica permisos de cámara/galería
- **Error de lectura:** Archivo muy grande o formato no soportado
- **Análisis falla:** Modelo no soporta visión o no está cargado

### Rendimiento
- **App lenta:** Reduce tamaño de interfaz y texto
- **Memoria alta:** Reinicia la app en Expo Go
- **Respuestas lentas:** Verifica conexión de red

## 🔧 Desarrollo

### Estructura del Proyecto
```
src/
├── components/          # Componentes reutilizables
├── screens/            # Pantallas principales
├── services/           # Servicios de API y archivos
├── context/            # Estado global de la app
└── theme/              # Temas y estilos
```

### Scripts Disponibles
```bash
npm start              # Iniciar servidor de desarrollo
npm run android        # Abrir en Android
npm run ios           # Abrir en iOS
npm run web           # Abrir en navegador
```

### Personalización
- **Temas:** Modifica `src/theme/theme.js`
- **Colores:** Ajusta paleta en el tema
- **Componentes:** Extiende componentes existentes
- **Servicios:** Agrega nuevos servicios de API

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📞 Soporte

Si tienes problemas o preguntas:
1. Revisa la sección de **Solución de Problemas**
2. Verifica que LM Studio esté configurado correctamente
3. Asegúrate de tener la última versión de Expo Go

---

**Desarrollado con ❤️ para la comunidad de LM Studio**
