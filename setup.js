#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 LM Studio Chat Mobile - Setup Script');
console.log('=====================================\n');

// Verificar Node.js
function checkNodeVersion() {
  console.log('📋 Verificando Node.js...');
  try {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 16) {
      console.error('❌ Node.js 16+ requerido. Versión actual:', nodeVersion);
      console.log('📥 Descarga desde: https://nodejs.org/');
      process.exit(1);
    }
    
    console.log('✅ Node.js', nodeVersion, 'detectado');
  } catch (error) {
    console.error('❌ Error verificando Node.js:', error.message);
    process.exit(1);
  }
}

// Verificar/Instalar Expo CLI
function checkExpoCLI() {
  console.log('\n📋 Verificando Expo CLI...');
  try {
    execSync('expo --version', { stdio: 'pipe' });
    console.log('✅ Expo CLI ya está instalado');
  } catch (error) {
    console.log('📥 Instalando Expo CLI...');
    try {
      execSync('npm install -g expo-cli', { stdio: 'inherit' });
      console.log('✅ Expo CLI instalado exitosamente');
    } catch (installError) {
      console.error('❌ Error instalando Expo CLI:', installError.message);
      console.log('💡 Intenta manualmente: npm install -g expo-cli');
      process.exit(1);
    }
  }
}

// Instalar dependencias
function installDependencies() {
  console.log('\n📦 Instalando dependencias...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencias instaladas exitosamente');
  } catch (error) {
    console.error('❌ Error instalando dependencias:', error.message);
    console.log('💡 Intenta manualmente: npm install');
    process.exit(1);
  }
}

// Verificar estructura de archivos
function checkProjectStructure() {
  console.log('\n📁 Verificando estructura del proyecto...');
  
  const requiredFiles = [
    'App.js',
    'package.json',
    'app.json',
    'babel.config.js',
    'src/screens/ChatScreen.js',
    'src/screens/SettingsScreen.js',
    'src/screens/OthersScreen.js',
    'src/context/AppContext.js',
    'src/theme/theme.js',
    'src/services/LMStudioService.js',
    'src/services/FileAttachmentService.js',
  ];
  
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    console.error('❌ Archivos faltantes:');
    missingFiles.forEach(file => console.error('  -', file));
    console.log('💡 Asegúrate de tener todos los archivos del proyecto');
    process.exit(1);
  }
  
  console.log('✅ Estructura del proyecto verificada');
}

// Crear directorio de assets si no existe
function createAssetsDirectory() {
  console.log('\n📂 Configurando directorio de assets...');
  
  const assetsDir = 'assets';
  if (!fs.existsSync(assetsDir)) {
    fs.mkdirSync(assetsDir);
    console.log('✅ Directorio assets creado');
  } else {
    console.log('✅ Directorio assets ya existe');
  }
  
  // Crear archivos de placeholder para iconos si no existen
  const iconFiles = [
    'icon.png',
    'splash.png',
    'adaptive-icon.png',
    'favicon.png'
  ];
  
  iconFiles.forEach(iconFile => {
    const iconPath = path.join(assetsDir, iconFile);
    if (!fs.existsSync(iconPath)) {
      // Crear archivo placeholder
      fs.writeFileSync(iconPath, '');
      console.log('📝 Placeholder creado para', iconFile);
    }
  });
}

// Verificar configuración de Expo
function checkExpoConfig() {
  console.log('\n⚙️ Verificando configuración de Expo...');
  
  try {
    const appConfig = JSON.parse(fs.readFileSync('app.json', 'utf8'));
    
    if (!appConfig.expo) {
      console.error('❌ Configuración de Expo inválida en app.json');
      process.exit(1);
    }
    
    console.log('✅ Configuración de Expo válida');
    console.log('📱 Nombre de la app:', appConfig.expo.name);
    console.log('🔖 Versión:', appConfig.expo.version);
    
  } catch (error) {
    console.error('❌ Error leyendo app.json:', error.message);
    process.exit(1);
  }
}

// Mostrar información de red
function showNetworkInfo() {
  console.log('\n🌐 Información de Red:');
  console.log('======================');
  
  const os = require('os');
  const interfaces = os.networkInterfaces();
  
  Object.keys(interfaces).forEach(name => {
    const iface = interfaces[name];
    const ipv4 = iface.find(addr => addr.family === 'IPv4' && !addr.internal);
    
    if (ipv4) {
      console.log(`📡 ${name}: ${ipv4.address}`);
    }
  });
  
  console.log('\n💡 Usa una de estas IPs para configurar LM Studio');
  console.log('   Formato: http://IP:1234 (ej: http://*************:1234)');
}

// Mostrar instrucciones finales
function showFinalInstructions() {
  console.log('\n🎉 ¡Setup Completado!');
  console.log('====================\n');
  
  console.log('📱 Próximos pasos:');
  console.log('1. Instalar "Expo Go" en tu móvil desde:');
  console.log('   📱 Android: Google Play Store');
  console.log('   📱 iOS: App Store\n');
  
  console.log('2. Configurar LM Studio en tu PC:');
  console.log('   🖥️  Descargar desde: https://lmstudio.ai/');
  console.log('   📥 Cargar un modelo (recomendado: gemma-2-2b-it)');
  console.log('   🚀 Iniciar servidor en puerto 1234\n');
  
  console.log('3. Iniciar la aplicación:');
  console.log('   💻 Ejecutar: npm start');
  console.log('   📱 Escanear QR con Expo Go');
  console.log('   ⚙️  Configurar URL del servidor en la app\n');
  
  console.log('📚 Documentación completa en: README_MOBILE.md');
  console.log('🛠️  Guía de instalación en: INSTALACION.md\n');
  
  console.log('🚀 Para iniciar ahora: npm start');
}

// Función principal
function main() {
  try {
    checkNodeVersion();
    checkExpoCLI();
    checkProjectStructure();
    installDependencies();
    createAssetsDirectory();
    checkExpoConfig();
    showNetworkInfo();
    showFinalInstructions();
    
  } catch (error) {
    console.error('\n❌ Error durante el setup:', error.message);
    console.log('\n💡 Si el problema persiste:');
    console.log('   1. Verifica que Node.js esté instalado correctamente');
    console.log('   2. Ejecuta: npm cache clean --force');
    console.log('   3. Elimina node_modules y ejecuta: npm install');
    console.log('   4. Consulta INSTALACION.md para más detalles');
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}

module.exports = {
  checkNodeVersion,
  checkExpoCLI,
  installDependencies,
  checkProjectStructure,
  createAssetsDirectory,
  checkExpoConfig,
  showNetworkInfo,
  showFinalInstructions,
  main
};
