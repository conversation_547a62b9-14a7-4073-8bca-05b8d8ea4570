<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 LM Studio Chat - Simple</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #1a1a2e;
            color: white;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 1.2em;
            font-weight: 600;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .modal.active {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: #1a1a2e;
            padding: 25px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.3em;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 80%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease;
        }

        .message.system {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            text-align: center;
            margin: 10px auto;
            max-width: 90%;
            font-size: 0.9em;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-message {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
            color: #51cf66;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        .error-message {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.5);
            color: #ff6b6b;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>🚀 LM Studio Chat - Simple</h1>
            <div class="header-actions">
                <button class="btn" onclick="testModal()">🧪 Test Modal</button>
                <button class="btn" onclick="openSettings()">⚙️ Config</button>
            </div>
        </div>

        <div class="main-content">
            <div class="chat-container" id="chatContainer">
                <div class="message system">
                    🚀 <strong>¡Bienvenido a LM Studio Chat Simple!</strong><br><br>
                    Esta es una versión simplificada para probar que todo funcione correctamente.<br><br>
                    Haz clic en "⚙️ Config" para probar el modal de configuración.
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Configuración -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Configuración</h2>
                <button class="close-btn" onclick="closeModal('settingsModal')">&times;</button>
            </div>
            <div class="form-group">
                <label class="form-label">Servidor LM Studio</label>
                <input type="text" class="form-input" id="serverUrl" placeholder="http://localhost:1234" value="http://localhost:1234">
            </div>
            <div class="form-group">
                <button class="btn" onclick="testConnection()">🔗 Probar Conexión</button>
            </div>
            <div id="connectionStatus"></div>
        </div>
    </div>

    <!-- Modal de Test -->
    <div class="modal" id="testModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Modal de Prueba</h2>
                <button class="close-btn" onclick="closeModal('testModal')">&times;</button>
            </div>
            <div class="success-message">
                ✅ ¡El modal funciona correctamente!<br>
                JavaScript está ejecutándose sin problemas.
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 JavaScript cargado correctamente');

        // Variables globales
        let isConnected = false;

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 DOM cargado, aplicación lista');
            addMessage('system', '✅ JavaScript funcionando correctamente');
        });

        // Funciones de modales
        function openModal(modalId) {
            console.log('🔓 Abriendo modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                console.log('✅ Modal abierto:', modalId);
            } else {
                console.error('❌ Modal no encontrado:', modalId);
                alert('Error: Modal no encontrado - ' + modalId);
            }
        }

        function closeModal(modalId) {
            console.log('🔒 Cerrando modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                console.log('✅ Modal cerrado:', modalId);
            } else {
                console.error('❌ Modal no encontrado:', modalId);
            }
        }

        function openSettings() {
            console.log('⚙️ Función openSettings llamada');
            openModal('settingsModal');
        }

        function testModal() {
            console.log('🧪 Función testModal llamada');
            openModal('testModal');
        }

        function addMessage(role, content) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.innerHTML = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        async function testConnection() {
            const serverUrl = document.getElementById('serverUrl').value;
            const statusDiv = document.getElementById('connectionStatus');
            
            try {
                statusDiv.innerHTML = '<div class="success-message">Probando conexión...</div>';
                console.log('🔗 Probando conexión a:', serverUrl);
                
                const response = await fetch(`${serverUrl}/v1/models`);
                if (response.ok) {
                    isConnected = true;
                    statusDiv.innerHTML = '<div class="success-message">✅ Conexión exitosa!</div>';
                    console.log('✅ Conexión exitosa');
                } else {
                    throw new Error('Error de conexión');
                }
            } catch (error) {
                isConnected = false;
                statusDiv.innerHTML = '<div class="error-message">❌ Error de conexión. Verifica que LM Studio esté ejecutándose.</div>';
                console.error('❌ Error de conexión:', error);
            }
        }

        // Test inicial
        setTimeout(() => {
            console.log('🎯 Test automático ejecutándose...');
            addMessage('system', '🎯 Test automático: JavaScript funcionando correctamente');
        }, 1000);
    </script>
</body>
</html>
