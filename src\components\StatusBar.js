import React from 'react';
import {
  View,
  StyleSheet,
} from 'react-native';
import {
  Text,
  Chip,
  Surface,
} from 'react-native-paper';

import { useApp } from '../context/AppContext';
import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';
import { isVisionModel } from '../services/LMStudioService';

export default function StatusBar() {
  const { state } = useApp();
  const { 
    isConnected, 
    currentLoadedModel, 
    currentCharacter, 
    uiScale, 
    textScale 
  } = state;

  const styles = createStyles(uiScale, textScale);

  return (
    <Surface style={styles.container}>
      <View style={styles.statusRow}>
        {/* Estado del Modelo */}
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Modelo:</Text>
          {currentLoadedModel ? (
            <View style={styles.modelContainer}>
              {isVisionModel(currentLoadedModel) && (
                <Text style={styles.visionIcon}>👁️</Text>
              )}
              <Text style={styles.statusValueActive} numberOfLines={1}>
                {currentLoadedModel}
              </Text>
            </View>
          ) : (
            <Text style={styles.statusValueInactive}>
              No cargado
            </Text>
          )}
        </View>

        {/* Estado del Personaje */}
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Personaje:</Text>
          {currentCharacter ? (
            <Text style={styles.statusValueActive} numberOfLines={1}>
              {currentCharacter.emoji} {currentCharacter.name}
            </Text>
          ) : (
            <Text style={styles.statusValueInactive}>
              Ninguno
            </Text>
          )}
        </View>
      </View>

      {/* Indicador de Conexión */}
      <View style={styles.connectionRow}>
        <Chip
          icon={isConnected ? 'check-circle' : 'close-circle'}
          style={[
            styles.connectionChip,
            { backgroundColor: isConnected ? 'rgba(40, 167, 69, 0.2)' : 'rgba(220, 53, 69, 0.2)' }
          ]}
          textStyle={[
            styles.connectionText,
            { color: isConnected ? '#51cf66' : '#ff6b6b' }
          ]}
        >
          {isConnected ? 'Conectado' : 'Desconectado'}
        </Chip>
      </View>
    </Surface>
  );
}

const createStyles = (uiScale, textScale) => StyleSheet.create({
  container: {
    backgroundColor: '#1a1a2e',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: getSpacing(16, uiScale),
    paddingVertical: getSpacing(12, uiScale),
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: getSpacing(8, uiScale),
  },
  statusItem: {
    flex: 1,
    marginHorizontal: getSpacing(4, uiScale),
    padding: getSpacing(8, uiScale),
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: getScaledSize(8, uiScale),
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  statusLabel: {
    fontSize: getFontSize(11, textScale),
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: getSpacing(2, uiScale),
  },
  modelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  visionIcon: {
    fontSize: getFontSize(12, textScale),
    marginRight: getSpacing(4, uiScale),
  },
  statusValueActive: {
    fontSize: getFontSize(12, textScale),
    color: '#51cf66',
    fontWeight: '500',
    flex: 1,
  },
  statusValueInactive: {
    fontSize: getFontSize(12, textScale),
    color: 'rgba(255, 255, 255, 0.5)',
  },
  connectionRow: {
    alignItems: 'center',
  },
  connectionChip: {
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  connectionText: {
    fontSize: getFontSize(12, textScale),
    fontWeight: '500',
  },
});
