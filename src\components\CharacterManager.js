import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  TextInput,
  Chip,
  Divider,
  IconButton,
} from 'react-native-paper';

import { useApp } from '../context/AppContext';
import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';

const defaultCharacters = [
  {
    id: 'assistant',
    name: 'Asist<PERSON>',
    emoji: '🤖',
    prompt: 'Eres un asistente útil y amigable. Responde de manera clara y concisa.',
    isDefault: true,
  },
  {
    id: 'teacher',
    name: '<PERSON><PERSON><PERSON>',
    emoji: '👨‍🏫',
    prompt: 'Eres un profesor experto y paciente. Explica conceptos de manera didáctica y con ejemplos.',
    isDefault: true,
  },
  {
    id: 'creative',
    name: 'Creativo',
    emoji: '🎨',
    prompt: 'Eres un asistente creativo e imaginativo. Ayuda con ideas originales y soluciones innovadoras.',
    isDefault: true,
  },
  {
    id: 'analyst',
    name: '<PERSON><PERSON><PERSON>',
    emoji: '📊',
    prompt: 'Eres un analista detallado y metódico. Proporciona análisis profundos y basados en datos.',
    isDefault: true,
  },
];

export default function CharacterManager({ uiScale, textScale }) {
  const { state, dispatch, addSystemLog } = useApp();
  const [characters, setCharacters] = useState([]);
  const [isCreating, setIsCreating] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [newCharacter, setNewCharacter] = useState({
    name: '',
    emoji: '🤖',
    prompt: '',
  });

  const { currentCharacter } = state;

  useEffect(() => {
    // Inicializar con personajes por defecto si no hay ninguno
    if (characters.length === 0) {
      setCharacters(defaultCharacters);
    }
  }, []);

  const selectCharacter = (character) => {
    dispatch({ type: 'SET_CHARACTER', payload: character });
    addSystemLog('success', `Personaje "${character.name}" seleccionado`);
  };

  const clearCharacter = () => {
    dispatch({ type: 'SET_CHARACTER', payload: null });
    addSystemLog('info', 'Personaje deseleccionado');
  };

  const startCreating = () => {
    setIsCreating(true);
    setNewCharacter({
      name: '',
      emoji: '🤖',
      prompt: '',
    });
  };

  const cancelCreating = () => {
    setIsCreating(false);
    setNewCharacter({
      name: '',
      emoji: '🤖',
      prompt: '',
    });
  };

  const createCharacter = () => {
    if (!newCharacter.name.trim()) {
      Alert.alert('Error', 'Ingresa un nombre para el personaje');
      return;
    }

    if (!newCharacter.prompt.trim()) {
      Alert.alert('Error', 'Ingresa un prompt para el personaje');
      return;
    }

    const character = {
      id: Date.now().toString(),
      name: newCharacter.name.trim(),
      emoji: newCharacter.emoji,
      prompt: newCharacter.prompt.trim(),
      isDefault: false,
      createdAt: new Date().toISOString(),
    };

    const updatedCharacters = [...characters, character];
    setCharacters(updatedCharacters);
    setIsCreating(false);
    setNewCharacter({
      name: '',
      emoji: '🤖',
      prompt: '',
    });

    addSystemLog('success', `Personaje "${character.name}" creado`);
  };

  const startEditing = (character) => {
    setEditingId(character.id);
    setNewCharacter({
      name: character.name,
      emoji: character.emoji,
      prompt: character.prompt,
    });
  };

  const saveEdit = () => {
    if (!newCharacter.name.trim() || !newCharacter.prompt.trim()) {
      Alert.alert('Error', 'Completa todos los campos');
      return;
    }

    const updatedCharacters = characters.map(char =>
      char.id === editingId
        ? {
            ...char,
            name: newCharacter.name.trim(),
            emoji: newCharacter.emoji,
            prompt: newCharacter.prompt.trim(),
          }
        : char
    );

    setCharacters(updatedCharacters);
    
    // Actualizar personaje actual si es el que se está editando
    if (currentCharacter?.id === editingId) {
      const updatedCurrent = updatedCharacters.find(char => char.id === editingId);
      dispatch({ type: 'SET_CHARACTER', payload: updatedCurrent });
    }

    setEditingId(null);
    setNewCharacter({
      name: '',
      emoji: '🤖',
      prompt: '',
    });

    addSystemLog('success', 'Personaje actualizado');
  };

  const cancelEdit = () => {
    setEditingId(null);
    setNewCharacter({
      name: '',
      emoji: '🤖',
      prompt: '',
    });
  };

  const deleteCharacter = (character) => {
    if (character.isDefault) {
      Alert.alert('Error', 'No puedes eliminar personajes predefinidos');
      return;
    }

    Alert.alert(
      'Eliminar Personaje',
      `¿Estás seguro de que quieres eliminar "${character.name}"?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: () => {
            const updatedCharacters = characters.filter(char => char.id !== character.id);
            setCharacters(updatedCharacters);
            
            // Si se elimina el personaje actual, deseleccionar
            if (currentCharacter?.id === character.id) {
              dispatch({ type: 'SET_CHARACTER', payload: null });
            }
            
            addSystemLog('info', `Personaje "${character.name}" eliminado`);
          },
        },
      ]
    );
  };

  const emojiOptions = ['🤖', '👨‍🏫', '👩‍🏫', '🎨', '📊', '💼', '🔬', '👨‍💻', '👩‍💻', '🧠', '💡', '🎭', '📚', '🌟'];

  const styles = createStyles(uiScale, textScale);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      
      {/* Personaje Actual */}
      <Card style={styles.card}>
        <Card.Title title="👤 Personaje Actual" titleStyle={styles.cardTitle} />
        <Card.Content>
          {currentCharacter ? (
            <View style={styles.currentCharacter}>
              <View style={styles.characterHeader}>
                <Text style={styles.characterEmoji}>{currentCharacter.emoji}</Text>
                <Text style={styles.characterName}>{currentCharacter.name}</Text>
              </View>
              <Text style={styles.characterPrompt}>{currentCharacter.prompt}</Text>
              <Button
                mode="outlined"
                onPress={clearCharacter}
                style={styles.clearButton}
                contentStyle={styles.buttonContent}
                textColor="#ff6b6b"
              >
                🗑️ Deseleccionar
              </Button>
            </View>
          ) : (
            <Text style={styles.noCharacterText}>
              Ningún personaje seleccionado. La IA usará su comportamiento por defecto.
            </Text>
          )}
        </Card.Content>
      </Card>

      {/* Lista de Personajes */}
      <Card style={styles.card}>
        <Card.Title title="🎭 Personajes Disponibles" titleStyle={styles.cardTitle} />
        <Card.Content>
          {characters.map((character, index) => (
            <View key={character.id}>
              {editingId === character.id ? (
                <View style={styles.editForm}>
                  <View style={styles.formRow}>
                    <TextInput
                      label="Nombre"
                      value={newCharacter.name}
                      onChangeText={(text) => setNewCharacter({...newCharacter, name: text})}
                      style={styles.nameInput}
                      theme={{
                        colors: {
                          primary: '#667eea',
                          onSurface: '#ffffff',
                          surface: 'rgba(255, 255, 255, 0.1)',
                        }
                      }}
                    />
                    <View style={styles.emojiContainer}>
                      <Text style={styles.selectedEmoji}>{newCharacter.emoji}</Text>
                    </View>
                  </View>
                  
                  <View style={styles.emojiSelector}>
                    {emojiOptions.map((emoji) => (
                      <Button
                        key={emoji}
                        mode={newCharacter.emoji === emoji ? 'contained' : 'outlined'}
                        onPress={() => setNewCharacter({...newCharacter, emoji})}
                        style={styles.emojiButton}
                        contentStyle={styles.emojiButtonContent}
                      >
                        {emoji}
                      </Button>
                    ))}
                  </View>
                  
                  <TextInput
                    label="Prompt del Sistema"
                    value={newCharacter.prompt}
                    onChangeText={(text) => setNewCharacter({...newCharacter, prompt: text})}
                    multiline
                    numberOfLines={4}
                    style={styles.promptInput}
                    theme={{
                      colors: {
                        primary: '#667eea',
                        onSurface: '#ffffff',
                        surface: 'rgba(255, 255, 255, 0.1)',
                      }
                    }}
                  />
                  
                  <View style={styles.editActions}>
                    <Button
                      mode="contained"
                      onPress={saveEdit}
                      style={styles.saveButton}
                      contentStyle={styles.buttonContent}
                    >
                      ✅ Guardar
                    </Button>
                    <Button
                      mode="outlined"
                      onPress={cancelEdit}
                      style={styles.cancelButton}
                      contentStyle={styles.buttonContent}
                    >
                      ❌ Cancelar
                    </Button>
                  </View>
                </View>
              ) : (
                <View style={styles.characterItem}>
                  <View style={styles.characterInfo}>
                    <View style={styles.characterHeader}>
                      <Text style={styles.characterEmoji}>{character.emoji}</Text>
                      <Text style={[
                        styles.characterName,
                        currentCharacter?.id === character.id && styles.selectedCharacterName
                      ]}>
                        {character.name}
                        {currentCharacter?.id === character.id && ' ✅'}
                      </Text>
                      {character.isDefault && (
                        <Chip style={styles.defaultChip} textStyle={styles.defaultChipText}>
                          Por defecto
                        </Chip>
                      )}
                    </View>
                    <Text style={styles.characterPrompt} numberOfLines={2}>
                      {character.prompt}
                    </Text>
                  </View>
                  
                  <View style={styles.characterActions}>
                    <IconButton
                      icon="play"
                      size={getScaledSize(20, uiScale)}
                      iconColor="#667eea"
                      onPress={() => selectCharacter(character)}
                    />
                    <IconButton
                      icon="pencil"
                      size={getScaledSize(20, uiScale)}
                      iconColor="#ffd43b"
                      onPress={() => startEditing(character)}
                    />
                    {!character.isDefault && (
                      <IconButton
                        icon="delete"
                        size={getScaledSize(20, uiScale)}
                        iconColor="#ff6b6b"
                        onPress={() => deleteCharacter(character)}
                      />
                    )}
                  </View>
                </View>
              )}
              
              {index < characters.length - 1 && <Divider style={styles.divider} />}
            </View>
          ))}
        </Card.Content>
      </Card>

      {/* Crear Nuevo Personaje */}
      <Card style={styles.card}>
        <Card.Title title="➕ Crear Personaje" titleStyle={styles.cardTitle} />
        <Card.Content>
          {isCreating ? (
            <View style={styles.createForm}>
              <View style={styles.formRow}>
                <TextInput
                  label="Nombre del Personaje"
                  value={newCharacter.name}
                  onChangeText={(text) => setNewCharacter({...newCharacter, name: text})}
                  style={styles.nameInput}
                  theme={{
                    colors: {
                      primary: '#667eea',
                      onSurface: '#ffffff',
                      surface: 'rgba(255, 255, 255, 0.1)',
                    }
                  }}
                />
                <View style={styles.emojiContainer}>
                  <Text style={styles.selectedEmoji}>{newCharacter.emoji}</Text>
                </View>
              </View>
              
              <View style={styles.emojiSelector}>
                {emojiOptions.map((emoji) => (
                  <Button
                    key={emoji}
                    mode={newCharacter.emoji === emoji ? 'contained' : 'outlined'}
                    onPress={() => setNewCharacter({...newCharacter, emoji})}
                    style={styles.emojiButton}
                    contentStyle={styles.emojiButtonContent}
                  >
                    {emoji}
                  </Button>
                ))}
              </View>
              
              <TextInput
                label="Prompt del Sistema"
                value={newCharacter.prompt}
                onChangeText={(text) => setNewCharacter({...newCharacter, prompt: text})}
                placeholder="Describe cómo debe comportarse la IA..."
                multiline
                numberOfLines={4}
                style={styles.promptInput}
                theme={{
                  colors: {
                    primary: '#667eea',
                    onSurface: '#ffffff',
                    surface: 'rgba(255, 255, 255, 0.1)',
                  }
                }}
              />
              
              <View style={styles.createActions}>
                <Button
                  mode="contained"
                  onPress={createCharacter}
                  style={styles.createButton}
                  contentStyle={styles.buttonContent}
                >
                  ✅ Crear Personaje
                </Button>
                <Button
                  mode="outlined"
                  onPress={cancelCreating}
                  style={styles.cancelButton}
                  contentStyle={styles.buttonContent}
                >
                  ❌ Cancelar
                </Button>
              </View>
            </View>
          ) : (
            <Button
              mode="contained"
              onPress={startCreating}
              style={styles.startCreateButton}
              contentStyle={styles.buttonContent}
            >
              ➕ Crear Nuevo Personaje
            </Button>
          )}
        </Card.Content>
      </Card>

    </ScrollView>
  );
}

const createStyles = (uiScale, textScale) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  content: {
    padding: getSpacing(16, uiScale),
    paddingBottom: getSpacing(32, uiScale),
  },
  card: {
    marginBottom: getSpacing(16, uiScale),
    backgroundColor: '#1a1a2e',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
  },
  cardTitle: {
    fontSize: getFontSize(16, textScale),
    color: '#ffffff',
    fontWeight: 'bold',
  },
  currentCharacter: {
    padding: getSpacing(12, uiScale),
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderRadius: getScaledSize(8, uiScale),
    borderWidth: 1,
    borderColor: 'rgba(102, 126, 234, 0.3)',
  },
  noCharacterText: {
    fontSize: getFontSize(14, textScale),
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: getSpacing(20, uiScale),
  },
  characterItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: getSpacing(8, uiScale),
  },
  characterInfo: {
    flex: 1,
    marginRight: getSpacing(8, uiScale),
  },
  characterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getSpacing(4, uiScale),
    flexWrap: 'wrap',
  },
  characterEmoji: {
    fontSize: getFontSize(20, textScale),
    marginRight: getSpacing(8, uiScale),
  },
  characterName: {
    fontSize: getFontSize(16, textScale),
    color: '#ffffff',
    fontWeight: '500',
    marginRight: getSpacing(8, uiScale),
  },
  selectedCharacterName: {
    color: '#51cf66',
  },
  characterPrompt: {
    fontSize: getFontSize(13, textScale),
    color: 'rgba(255, 255, 255, 0.7)',
    lineHeight: getFontSize(18, textScale),
  },
  characterActions: {
    flexDirection: 'row',
  },
  defaultChip: {
    backgroundColor: 'rgba(255, 193, 7, 0.2)',
    borderColor: 'rgba(255, 193, 7, 0.4)',
    borderWidth: 1,
  },
  defaultChipText: {
    fontSize: getFontSize(10, textScale),
    color: '#ffd43b',
  },
  createForm: {
    gap: getSpacing(16, uiScale),
  },
  editForm: {
    gap: getSpacing(16, uiScale),
    padding: getSpacing(12, uiScale),
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: getScaledSize(8, uiScale),
  },
  formRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getSpacing(12, uiScale),
  },
  nameInput: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    fontSize: getFontSize(16, textScale),
  },
  emojiContainer: {
    width: getScaledSize(50, uiScale),
    height: getScaledSize(50, uiScale),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: getScaledSize(8, uiScale),
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedEmoji: {
    fontSize: getFontSize(24, textScale),
  },
  emojiSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getSpacing(8, uiScale),
  },
  emojiButton: {
    minWidth: getScaledSize(40, uiScale),
  },
  emojiButtonContent: {
    height: getScaledSize(35, uiScale),
  },
  promptInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    fontSize: getFontSize(14, textScale),
    minHeight: getScaledSize(100, uiScale),
  },
  createActions: {
    flexDirection: 'row',
    gap: getSpacing(8, uiScale),
  },
  editActions: {
    flexDirection: 'row',
    gap: getSpacing(8, uiScale),
  },
  createButton: {
    flex: 1,
    backgroundColor: '#667eea',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#51cf66',
  },
  startCreateButton: {
    backgroundColor: '#667eea',
  },
  cancelButton: {
    flex: 1,
    borderColor: '#ff6b6b',
  },
  clearButton: {
    borderColor: '#ff6b6b',
    marginTop: getSpacing(8, uiScale),
  },
  buttonContent: {
    height: getScaledSize(40, uiScale),
  },
  divider: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: getSpacing(8, uiScale),
  },
});
