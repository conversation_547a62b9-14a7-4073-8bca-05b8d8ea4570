import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Provider as PaperProvider } from 'react-native-paper';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import ChatScreen from './src/screens/ChatScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import OthersScreen from './src/screens/OthersScreen';
import { AppProvider } from './src/context/AppContext';
import { darkTheme } from './src/theme/theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

const Tab = createBottomTabNavigator();

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <PaperProvider theme={darkTheme}>
        <AppProvider>
          <NavigationContainer theme={darkTheme}>
            <StatusBar style="light" backgroundColor="#1a1a2e" />
            <Tab.Navigator
              screenOptions={({ route }) => ({
                tabBarIcon: ({ focused, color, size }) => {
                  let iconName;
                  
                  if (route.name === 'Chat') {
                    iconName = 'chat';
                  } else if (route.name === 'Configuración') {
                    iconName = 'settings';
                  } else if (route.name === 'Otros') {
                    iconName = 'more-horiz';
                  }
                  
                  return <Icon name={iconName} size={size} color={color} />;
                },
                tabBarActiveTintColor: '#667eea',
                tabBarInactiveTintColor: 'rgba(255, 255, 255, 0.6)',
                tabBarStyle: {
                  backgroundColor: '#1a1a2e',
                  borderTopColor: 'rgba(255, 255, 255, 0.1)',
                  borderTopWidth: 1,
                },
                headerStyle: {
                  backgroundColor: '#1a1a2e',
                  borderBottomColor: 'rgba(255, 255, 255, 0.1)',
                  borderBottomWidth: 1,
                },
                headerTintColor: '#fff',
                headerTitleStyle: {
                  fontWeight: 'bold',
                },
              })}
            >
              <Tab.Screen 
                name="Chat" 
                component={ChatScreen}
                options={{
                  title: '🚀 LM Studio Chat',
                }}
              />
              <Tab.Screen 
                name="Configuración" 
                component={SettingsScreen}
                options={{
                  title: '⚙️ Configuración',
                }}
              />
              <Tab.Screen 
                name="Otros" 
                component={OthersScreen}
                options={{
                  title: '📋 Otros',
                }}
              />
            </Tab.Navigator>
          </NavigationContainer>
        </AppProvider>
      </PaperProvider>
    </GestureHandlerRootView>
  );
}
