import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  IconButton,
  Divider,
} from 'react-native-paper';

import { useApp } from '../context/AppContext';
import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';

export default function SystemLogs({ uiScale, textScale }) {
  const { state, dispatch, addSystemLog } = useApp();
  const [filter, setFilter] = useState('all');
  
  const { systemLogs } = state;

  const getLogIcon = (type) => {
    switch (type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📝';
    }
  };

  const getLogColor = (type) => {
    switch (type) {
      case 'success':
        return '#51cf66';
      case 'error':
        return '#ff6b6b';
      case 'warning':
        return '#ffd43b';
      case 'info':
        return '#667eea';
      default:
        return 'rgba(255, 255, 255, 0.8)';
    }
  };

  const filteredLogs = filter === 'all' 
    ? systemLogs 
    : systemLogs.filter(log => log.type === filter);

  const clearLogs = () => {
    Alert.alert(
      'Limpiar Logs',
      '¿Estás seguro de que quieres eliminar todos los logs?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Limpiar',
          style: 'destructive',
          onPress: () => {
            dispatch({ type: 'ADD_SYSTEM_LOG', payload: [] });
            addSystemLog('info', 'Logs del sistema limpiados');
          },
        },
      ]
    );
  };

  const exportLogs = () => {
    // TODO: Implementar exportación de logs
    addSystemLog('info', 'Función de exportación de logs en desarrollo');
  };

  const getLogStats = () => {
    const stats = {
      total: systemLogs.length,
      success: systemLogs.filter(log => log.type === 'success').length,
      error: systemLogs.filter(log => log.type === 'error').length,
      warning: systemLogs.filter(log => log.type === 'warning').length,
      info: systemLogs.filter(log => log.type === 'info').length,
    };
    return stats;
  };

  const stats = getLogStats();
  const styles = createStyles(uiScale, textScale);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      
      {/* Estadísticas */}
      <Card style={styles.card}>
        <Card.Title title="📊 Estadísticas de Logs" titleStyle={styles.cardTitle} />
        <Card.Content>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{stats.total}</Text>
              <Text style={styles.statLabel}>Total</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: '#51cf66' }]}>{stats.success}</Text>
              <Text style={styles.statLabel}>Éxito</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: '#ff6b6b' }]}>{stats.error}</Text>
              <Text style={styles.statLabel}>Error</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: '#ffd43b' }]}>{stats.warning}</Text>
              <Text style={styles.statLabel}>Advertencia</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: '#667eea' }]}>{stats.info}</Text>
              <Text style={styles.statLabel}>Info</Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Filtros y Acciones */}
      <Card style={styles.card}>
        <Card.Title title="🔍 Filtros y Acciones" titleStyle={styles.cardTitle} />
        <Card.Content>
          {/* Filtros */}
          <View style={styles.filtersContainer}>
            <Chip
              selected={filter === 'all'}
              onPress={() => setFilter('all')}
              style={[styles.filterChip, filter === 'all' && styles.selectedChip]}
              textStyle={[styles.filterChipText, filter === 'all' && styles.selectedChipText]}
            >
              Todos ({stats.total})
            </Chip>
            <Chip
              selected={filter === 'success'}
              onPress={() => setFilter('success')}
              style={[styles.filterChip, filter === 'success' && styles.selectedChip]}
              textStyle={[styles.filterChipText, filter === 'success' && styles.selectedChipText]}
            >
              ✅ Éxito ({stats.success})
            </Chip>
            <Chip
              selected={filter === 'error'}
              onPress={() => setFilter('error')}
              style={[styles.filterChip, filter === 'error' && styles.selectedChip]}
              textStyle={[styles.filterChipText, filter === 'error' && styles.selectedChipText]}
            >
              ❌ Error ({stats.error})
            </Chip>
            <Chip
              selected={filter === 'warning'}
              onPress={() => setFilter('warning')}
              style={[styles.filterChip, filter === 'warning' && styles.selectedChip]}
              textStyle={[styles.filterChipText, filter === 'warning' && styles.selectedChipText]}
            >
              ⚠️ Advertencia ({stats.warning})
            </Chip>
            <Chip
              selected={filter === 'info'}
              onPress={() => setFilter('info')}
              style={[styles.filterChip, filter === 'info' && styles.selectedChip]}
              textStyle={[styles.filterChipText, filter === 'info' && styles.selectedChipText]}
            >
              ℹ️ Info ({stats.info})
            </Chip>
          </View>

          {/* Acciones */}
          <View style={styles.actionsContainer}>
            <Button
              mode="outlined"
              onPress={exportLogs}
              style={styles.actionButton}
              contentStyle={styles.buttonContent}
            >
              📤 Exportar
            </Button>
            <Button
              mode="outlined"
              onPress={clearLogs}
              style={styles.clearButton}
              contentStyle={styles.buttonContent}
              textColor="#ff6b6b"
            >
              🗑️ Limpiar
            </Button>
          </View>
        </Card.Content>
      </Card>

      {/* Lista de Logs */}
      <Card style={styles.card}>
        <Card.Title 
          title={`📋 Logs del Sistema (${filteredLogs.length})`} 
          titleStyle={styles.cardTitle} 
        />
        <Card.Content>
          {filteredLogs.length === 0 ? (
            <Text style={styles.emptyText}>
              {filter === 'all' 
                ? 'No hay logs del sistema.' 
                : `No hay logs de tipo "${filter}".`}
            </Text>
          ) : (
            filteredLogs.map((log, index) => (
              <View key={log.id}>
                <View style={styles.logItem}>
                  <View style={styles.logHeader}>
                    <View style={styles.logTypeContainer}>
                      <Text style={styles.logIcon}>
                        {getLogIcon(log.type)}
                      </Text>
                      <Text style={[styles.logType, { color: getLogColor(log.type) }]}>
                        {log.type.toUpperCase()}
                      </Text>
                    </View>
                    <Text style={styles.logTime}>
                      {log.timestamp}
                    </Text>
                  </View>
                  <Text style={styles.logMessage}>
                    {log.message}
                  </Text>
                </View>
                
                {index < filteredLogs.length - 1 && <Divider style={styles.divider} />}
              </View>
            ))
          )}
        </Card.Content>
      </Card>

      {/* Información */}
      <Card style={styles.infoCard}>
        <Card.Content>
          <Text style={styles.infoText}>
            💡 Los logs se guardan automáticamente y se limitan a 100 entradas para mantener el rendimiento.
            {'\n\n'}
            🔍 Usa los filtros para encontrar logs específicos.
            {'\n\n'}
            📤 Exporta los logs para análisis técnico detallado.
          </Text>
        </Card.Content>
      </Card>

    </ScrollView>
  );
}

const createStyles = (uiScale, textScale) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  content: {
    padding: getSpacing(16, uiScale),
    paddingBottom: getSpacing(32, uiScale),
  },
  card: {
    marginBottom: getSpacing(16, uiScale),
    backgroundColor: '#1a1a2e',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
  },
  cardTitle: {
    fontSize: getFontSize(16, textScale),
    color: '#ffffff',
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: getSpacing(8, uiScale),
  },
  statItem: {
    alignItems: 'center',
    minWidth: getScaledSize(60, uiScale),
  },
  statNumber: {
    fontSize: getFontSize(20, textScale),
    fontWeight: 'bold',
    color: '#ffffff',
  },
  statLabel: {
    fontSize: getFontSize(12, textScale),
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: getSpacing(2, uiScale),
  },
  filtersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getSpacing(8, uiScale),
    marginBottom: getSpacing(16, uiScale),
  },
  filterChip: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
  },
  selectedChip: {
    backgroundColor: '#667eea',
  },
  filterChipText: {
    fontSize: getFontSize(12, textScale),
    color: 'rgba(255, 255, 255, 0.8)',
  },
  selectedChipText: {
    color: '#ffffff',
    fontWeight: '500',
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: getSpacing(8, uiScale),
  },
  actionButton: {
    flex: 1,
    borderColor: '#667eea',
  },
  clearButton: {
    flex: 1,
    borderColor: '#ff6b6b',
  },
  buttonContent: {
    height: getScaledSize(40, uiScale),
  },
  emptyText: {
    fontSize: getFontSize(14, textScale),
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: getSpacing(20, uiScale),
  },
  logItem: {
    paddingVertical: getSpacing(8, uiScale),
  },
  logHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: getSpacing(4, uiScale),
  },
  logTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logIcon: {
    fontSize: getFontSize(14, textScale),
    marginRight: getSpacing(6, uiScale),
  },
  logType: {
    fontSize: getFontSize(12, textScale),
    fontWeight: 'bold',
  },
  logTime: {
    fontSize: getFontSize(11, textScale),
    color: 'rgba(255, 255, 255, 0.5)',
  },
  logMessage: {
    fontSize: getFontSize(14, textScale),
    color: '#ffffff',
    lineHeight: getFontSize(20, textScale),
  },
  divider: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: getSpacing(8, uiScale),
  },
  infoCard: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderColor: 'rgba(102, 126, 234, 0.3)',
    borderWidth: 1,
  },
  infoText: {
    fontSize: getFontSize(13, textScale),
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: getFontSize(18, textScale),
  },
});
