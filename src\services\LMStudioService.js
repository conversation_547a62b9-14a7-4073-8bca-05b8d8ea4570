// Servicio para comunicación con LM Studio
export default class LMStudioService {
  
  // Función para detectar si el modelo soporta visión
  static isVisionModel(modelName) {
    const visionModels = [
      'gemma', 'llava', 'bakllava', 'moondream', 'cogvlm', 'qwen-vl', 
      'internvl', 'minicpm-v', 'phi-3-vision', 'pixtral', 'llama-vision',
      'gpt-4-vision', 'gpt-4o', 'claude-3', 'vision'
    ];
    
    const modelLower = modelName.toLowerCase();
    return visionModels.some(visionModel => modelLower.includes(visionModel));
  }

  // Probar conexión con LM Studio
  static async testConnection(serverUrl) {
    try {
      const response = await fetch(`${serverUrl}/v1/models`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 5000,
      });

      if (response.ok) {
        return { success: true, message: 'Conexión exitosa' };
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      return { 
        success: false, 
        message: error.message || 'Error de conexión' 
      };
    }
  }

  // Cargar lista de modelos disponibles
  static async loadModels(serverUrl) {
    try {
      const response = await fetch(`${serverUrl}/v1/models`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const models = data.data || [];
      
      // Detectar modelo actualmente cargado
      let currentLoadedModel = null;
      try {
        const loadedResponse = await this.detectLoadedModel(serverUrl);
        currentLoadedModel = loadedResponse.model;
      } catch (error) {
        console.log('No se pudo detectar modelo cargado:', error.message);
      }

      return {
        models: models,
        currentLoadedModel: currentLoadedModel,
      };
    } catch (error) {
      throw new Error(`Error cargando modelos: ${error.message}`);
    }
  }

  // Detectar modelo actualmente cargado
  static async detectLoadedModel(serverUrl) {
    try {
      // Método 1: Intentar obtener información del modelo activo desde la API v0
      try {
        const response = await fetch(`${serverUrl}/api/v0/models/loaded`);
        if (response.ok) {
          const data = await response.json();
          if (data.model) {
            return { model: data.model };
          }
        }
      } catch (error) {
        // Continuar con otros métodos
      }
      
      // Método 2: Intentar endpoint de estado
      try {
        const response = await fetch(`${serverUrl}/api/status`);
        if (response.ok) {
          const data = await response.json();
          if (data.loadedModel || data.model) {
            return { model: data.loadedModel || data.model };
          }
        }
      } catch (error) {
        // Continuar con otros métodos
      }
      
      return { model: null };
    } catch (error) {
      throw new Error(`Error detectando modelo: ${error.message}`);
    }
  }

  // Construir mensaje multimodal para modelos con visión
  static async buildMultimodalMessage(textMessage, files) {
    if (files.length === 0) {
      return { role: 'user', content: textMessage };
    }
    
    const imageFiles = files.filter(f => f.isImage);
    const textFiles = files.filter(f => !f.isImage);
    
    if (imageFiles.length === 0) {
      // Solo archivos de texto
      let fullMessage = textMessage;
      if (textFiles.length > 0) {
        fullMessage += '\n\n**Archivos adjuntos:**\n';
        textFiles.forEach((file, index) => {
          fullMessage += `\n**${index + 1}. ${file.name}** (${this.formatFileSize(file.size)})\n`;
          fullMessage += `Contenido:\n\`\`\`\n${file.content}\n\`\`\`\n`;
        });
      }
      return { role: 'user', content: fullMessage };
    }
    
    // Mensaje multimodal con imágenes
    const content = [];
    
    // Agregar texto si existe
    if (textMessage.trim()) {
      content.push({
        type: 'text',
        text: textMessage
      });
    }
    
    // Agregar archivos de texto si existen
    if (textFiles.length > 0) {
      let textContent = '\n\n**Documentos adjuntos:**\n';
      textFiles.forEach((file, index) => {
        textContent += `\n**${index + 1}. ${file.name}** (${this.formatFileSize(file.size)})\n`;
        textContent += `Contenido:\n\`\`\`\n${file.content}\n\`\`\`\n`;
      });
      
      content.push({
        type: 'text',
        text: textContent
      });
    }
    
    // Agregar imágenes
    imageFiles.forEach((file, index) => {
      content.push({
        type: 'image_url',
        image_url: {
          url: file.content, // URL completa con data:image/...;base64,
          detail: 'high' // Para análisis detallado
        }
      });
      
      // Agregar texto descriptivo para la imagen
      content.push({
        type: 'text',
        text: `\n**Imagen ${index + 1}: ${file.name}** (${this.formatFileSize(file.size)})\nPor favor, analiza esta imagen en detalle.`
      });
    });
    
    return {
      role: 'user',
      content: content
    };
  }

  // Función alternativa para modelos sin visión
  static async buildTextOnlyMessage(textMessage, files) {
    let fullMessage = textMessage;
    
    if (files.length > 0) {
      fullMessage += '\n\n**Archivos adjuntos:**\n';
      
      files.forEach((file, index) => {
        fullMessage += `\n**${index + 1}. ${file.name}** (${this.formatFileSize(file.size)})\n`;
        
        if (file.isImage) {
          fullMessage += `Tipo: Imagen\n`;
          fullMessage += `Descripción: Esta es una imagen llamada "${file.name}" de ${this.formatFileSize(file.size)}. `;
          fullMessage += `Como no puedo procesar imágenes directamente, por favor describe lo que `;
          fullMessage += `probablemente contiene una imagen con este nombre y proporciona un análisis general.\n`;
        } else {
          fullMessage += `Contenido:\n\`\`\`\n${file.content}\n\`\`\`\n`;
        }
      });
    }
    
    return { role: 'user', content: fullMessage };
  }

  // Enviar mensaje a LM Studio
  static async sendMessage(textMessage, attachedFiles, systemMessage, temperature, maxTokens, modelName, serverUrl) {
    try {
      const messages = [];
      
      // Agregar mensaje del sistema si existe
      if (systemMessage) {
        messages.push({ role: 'system', content: systemMessage });
      }
      
      // Construir mensaje del usuario
      const supportsVision = this.isVisionModel(modelName);
      let userMessage;
      
      if (attachedFiles.length > 0 && attachedFiles.some(f => f.isImage) && supportsVision) {
        userMessage = await this.buildMultimodalMessage(textMessage, attachedFiles);
      } else {
        userMessage = await this.buildTextOnlyMessage(textMessage, attachedFiles);
      }
      
      messages.push(userMessage);
      
      const requestBody = {
        model: modelName,
        messages: messages,
        temperature: temperature,
        max_tokens: maxTokens
      };

      const response = await fetch(`${serverUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.text();
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        
        try {
          const errorJson = JSON.parse(errorData);
          if (errorJson.error && errorJson.error.message) {
            errorMessage += ` - ${errorJson.error.message}`;
          }
        } catch (e) {
          // Error no es JSON válido
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('Respuesta inválida del servidor');
      }
      
      return data.choices[0].message.content;
      
    } catch (error) {
      throw new Error(`Error enviando mensaje: ${error.message}`);
    }
  }

  // Función auxiliar para formatear tamaño de archivo
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Exportar función isVisionModel para uso en otros componentes
export const isVisionModel = LMStudioService.isVisionModel;
