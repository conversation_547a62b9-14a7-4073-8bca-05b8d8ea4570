# 🚀 Inicio Rápido - LM Studio Chat Mobile

## ⚡ Setup en 5 Minutos

### 1. Preparación (2 min)
```bash
# Verificar Node.js (16+)
node --version

# Ejecutar setup automático
npm run setup

# Iniciar aplicación
npm start
```

### 2. En tu Móvil (1 min)
1. **Instalar Expo Go** desde tu tienda de apps
2. **Escanear QR** que aparece en la terminal
3. **Esperar** a que cargue la app

### 3. Configurar LM Studio (2 min)
1. **Abrir LM Studio** en tu PC
2. **Cargar modelo** (ej: gemma-2-2b-it)
3. **Iniciar servidor** (puerto 1234)
4. **Obtener IP** de tu PC: `ipconfig` (Windows) o `ifconfig` (Mac/Linux)

### 4. Conectar App (30 seg)
1. **Ir a "Configuración"** en la app
2. **Ingresar URL:** `http://TU_IP:1234`
3. **Probar conexión** ✅
4. **Cargar modelos** 📥

## 🎯 Primer Chat

### Chat Básico
```
👤 Tú: "Ho<PERSON>, ¿cómo estás?"
🤖 IA: "¡Hola! Estoy muy bien, gracias por preguntar..."
```

### Con Imagen
1. **Toca 📎** → **Galería** → **Seleccionar imagen**
2. **Escribir:** "¿Qué ves en esta imagen?"
3. **Enviar** → **IA analiza la imagen** 🖼️

### Con Documento
1. **Toca 📎** → **Documentos** → **Seleccionar PDF/TXT**
2. **Escribir:** "Resume este documento"
3. **Enviar** → **IA analiza el contenido** 📄

## ⚙️ Configuración Rápida

### Ajustar Interfaz
- **Tamaño UI:** 70% - 150% (slider en Configuración)
- **Tamaño Texto:** 80% - 180% (slider en Configuración)

### Seleccionar Personaje
1. **Otros** → **Gestión de Personajes**
2. **Seleccionar:** Asistente 🤖, Profesor 👨‍🏫, Creativo 🎨, Analista 📊
3. **O crear** tu propio personaje personalizado

### Gestionar Chats
1. **Otros** → **Gestión de Chats**
2. **Crear** nuevas conversaciones
3. **Renombrar** chats existentes
4. **Exportar** conversaciones importantes

## 🔧 Solución Rápida de Problemas

### No se conecta a LM Studio
```bash
# Verificar que LM Studio esté ejecutándose
# Verificar IP: ipconfig (Windows) / ifconfig (Mac/Linux)
# Verificar misma WiFi en PC y móvil
# Probar: http://IP:1234/v1/models en navegador
```

### App no carga
```bash
# Limpiar caché
npm run start:clear

# Verificar Expo Go actualizado
# Reiniciar Expo Go
```

### Archivos no se adjuntan
```bash
# Verificar permisos de cámara/galería
# Probar archivos más pequeños
# Verificar formato soportado
```

## 📱 Funciones Principales

### 💬 Chat
- **Conversaciones** fluidas con IA
- **Archivos adjuntos** (imágenes, documentos)
- **Múltiples chats** organizados
- **Edición** de mensajes (próximamente)

### 🖼️ Análisis de Imágenes
- **Modelos con visión** (LLaVA, Gemma, etc.)
- **Descripción** de imágenes
- **Lectura de texto** en imágenes
- **Análisis detallado** de contenido

### 📄 Análisis de Documentos
- **PDF, TXT, DOC** y más formatos
- **Resumen** automático
- **Preguntas** sobre el contenido
- **Extracción** de información clave

### 👤 Personajes
- **4 predefinidos** listos para usar
- **Creación personalizada** con prompts
- **Cambio dinámico** de personalidad
- **Aplicación automática** al chat

### ⚙️ Configuración
- **Conexión** a LM Studio
- **Selección** de modelos
- **Parámetros** de IA (temperatura, tokens)
- **Escalado** de interfaz y texto

### 📋 Gestión
- **Múltiples conversaciones**
- **Exportación** de chats
- **Logs del sistema**
- **Estadísticas** de uso

## 🎨 Personalización

### Temas y Colores
- **Tema oscuro** por defecto
- **Colores** adaptativos según función
- **Iconos** intuitivos y claros

### Escalado Adaptativo
- **UI Scale:** Ajusta botones, espacios, elementos
- **Text Scale:** Ajusta solo el tamaño de fuentes
- **Responsive:** Se adapta a cualquier pantalla

### Experiencia Móvil
- **Navegación por pestañas** intuitiva
- **Gestos táctiles** optimizados
- **Teclado inteligente** que se ajusta
- **Rendimiento** optimizado para móviles

## 🚀 Comandos Útiles

```bash
# Setup inicial
npm run setup

# Iniciar desarrollo
npm start

# Limpiar caché
npm run start:clear

# Usar túnel (problemas de red)
npm run start:tunnel

# Verificar configuración
npm run doctor

# Abrir en Android
npm run android

# Abrir en iOS
npm run ios

# Abrir en navegador
npm run web
```

## 📚 Recursos

- **📖 Documentación completa:** README_MOBILE.md
- **🛠️ Guía de instalación:** INSTALACION.md
- **🌐 LM Studio:** https://lmstudio.ai/
- **📱 Expo:** https://docs.expo.dev/
- **🎨 React Native Paper:** https://reactnativepaper.com/

## 💡 Tips Rápidos

### Para Mejor Rendimiento
- **Usar modelos pequeños** para móviles (2B-7B parámetros)
- **Reducir max_tokens** si las respuestas son lentas
- **Cerrar otras apps** en el móvil durante uso intensivo

### Para Mejor Experiencia
- **Ajustar escalado** según tu pantalla y preferencias
- **Crear personajes** para diferentes tipos de conversación
- **Organizar chats** por temas o proyectos
- **Usar archivos adjuntos** para análisis específicos

### Para Desarrollo
- **Usar túnel** si hay problemas de red local
- **Limpiar caché** si hay comportamientos extraños
- **Verificar logs** en "Otros → Logs" para debugging

---

¡Listo! 🎉 En 5 minutos tienes una app móvil completa para chatear con LM Studio.
