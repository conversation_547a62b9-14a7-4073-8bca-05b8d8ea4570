import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  TextInput,
  List,
  IconButton,
  Divider,
} from 'react-native-paper';

import { useApp } from '../context/AppContext';
import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';

export default function ConversationManager({ uiScale, textScale }) {
  const { state, dispatch, addSystemLog } = useApp();
  const [newConversationName, setNewConversationName] = useState('');
  const [editingId, setEditingId] = useState(null);
  const [editingName, setEditingName] = useState('');

  const { conversations, currentConversation } = state;

  const createNewConversation = () => {
    if (!newConversationName.trim()) {
      Alert.alert('Error', 'Ingresa un nombre para la conversación');
      return;
    }

    const newConversation = {
      id: Date.now().toString(),
      title: newConversationName.trim(),
      messages: [],
      characterId: state.currentCharacter?.id || null,
      characterName: state.currentCharacter?.name || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedConversations = [newConversation, ...conversations];
    dispatch({ type: 'SET_CONVERSATIONS', payload: updatedConversations });
    dispatch({ type: 'SET_CURRENT_CONVERSATION', payload: newConversation });
    
    setNewConversationName('');
    addSystemLog('success', `Conversación "${newConversation.title}" creada`);
  };

  const selectConversation = (conversation) => {
    dispatch({ type: 'SET_CURRENT_CONVERSATION', payload: conversation });
    addSystemLog('info', `Conversación "${conversation.title}" seleccionada`);
  };

  const startEditing = (conversation) => {
    setEditingId(conversation.id);
    setEditingName(conversation.title);
  };

  const saveEdit = () => {
    if (!editingName.trim()) {
      Alert.alert('Error', 'El nombre no puede estar vacío');
      return;
    }

    const updatedConversations = conversations.map(conv =>
      conv.id === editingId
        ? { ...conv, title: editingName.trim(), updatedAt: new Date().toISOString() }
        : conv
    );

    dispatch({ type: 'SET_CONVERSATIONS', payload: updatedConversations });
    
    // Actualizar conversación actual si es la que se está editando
    if (currentConversation?.id === editingId) {
      const updatedCurrent = updatedConversations.find(conv => conv.id === editingId);
      dispatch({ type: 'SET_CURRENT_CONVERSATION', payload: updatedCurrent });
    }

    setEditingId(null);
    setEditingName('');
    addSystemLog('success', 'Conversación renombrada');
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditingName('');
  };

  const deleteConversation = (conversation) => {
    Alert.alert(
      'Eliminar Conversación',
      `¿Estás seguro de que quieres eliminar "${conversation.title}"?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: () => {
            const updatedConversations = conversations.filter(conv => conv.id !== conversation.id);
            dispatch({ type: 'SET_CONVERSATIONS', payload: updatedConversations });
            
            // Si se elimina la conversación actual, seleccionar otra o crear nueva
            if (currentConversation?.id === conversation.id) {
              const nextConversation = updatedConversations[0] || null;
              dispatch({ type: 'SET_CURRENT_CONVERSATION', payload: nextConversation });
            }
            
            addSystemLog('info', `Conversación "${conversation.title}" eliminada`);
          },
        },
      ]
    );
  };

  const exportConversation = (conversation) => {
    // TODO: Implementar exportación
    addSystemLog('info', 'Función de exportación en desarrollo');
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const styles = createStyles(uiScale, textScale);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      
      {/* Crear Nueva Conversación */}
      <Card style={styles.card}>
        <Card.Title title="➕ Nueva Conversación" titleStyle={styles.cardTitle} />
        <Card.Content>
          <TextInput
            label="Nombre de la conversación"
            value={newConversationName}
            onChangeText={setNewConversationName}
            placeholder="Ej: Chat sobre programación"
            style={styles.input}
            theme={{
              colors: {
                primary: '#667eea',
                onSurface: '#ffffff',
                surface: 'rgba(255, 255, 255, 0.1)',
              }
            }}
          />
          <Button
            mode="contained"
            onPress={createNewConversation}
            disabled={!newConversationName.trim()}
            style={styles.createButton}
            contentStyle={styles.buttonContent}
          >
            ➕ Crear Conversación
          </Button>
        </Card.Content>
      </Card>

      {/* Lista de Conversaciones */}
      <Card style={styles.card}>
        <Card.Title title="💬 Conversaciones" titleStyle={styles.cardTitle} />
        <Card.Content>
          {conversations.length === 0 ? (
            <Text style={styles.emptyText}>
              No hay conversaciones. Crea una nueva para empezar.
            </Text>
          ) : (
            conversations.map((conversation, index) => (
              <View key={conversation.id}>
                <View style={styles.conversationItem}>
                  <View style={styles.conversationInfo}>
                    {editingId === conversation.id ? (
                      <View style={styles.editContainer}>
                        <TextInput
                          value={editingName}
                          onChangeText={setEditingName}
                          style={styles.editInput}
                          theme={{
                            colors: {
                              primary: '#667eea',
                              onSurface: '#ffffff',
                              surface: 'rgba(255, 255, 255, 0.1)',
                            }
                          }}
                        />
                        <View style={styles.editButtons}>
                          <IconButton
                            icon="check"
                            size={getScaledSize(20, uiScale)}
                            iconColor="#51cf66"
                            onPress={saveEdit}
                          />
                          <IconButton
                            icon="close"
                            size={getScaledSize(20, uiScale)}
                            iconColor="#ff6b6b"
                            onPress={cancelEdit}
                          />
                        </View>
                      </View>
                    ) : (
                      <>
                        <View style={styles.conversationHeader}>
                          <Text style={[
                            styles.conversationTitle,
                            currentConversation?.id === conversation.id && styles.currentConversation
                          ]}>
                            {conversation.title}
                            {currentConversation?.id === conversation.id && ' ✅'}
                          </Text>
                          {conversation.characterName && (
                            <Text style={styles.characterName}>
                              👤 {conversation.characterName}
                            </Text>
                          )}
                        </View>
                        <Text style={styles.conversationDate}>
                          {conversation.messages.length} mensajes • {formatDate(conversation.updatedAt)}
                        </Text>
                      </>
                    )}
                  </View>

                  {editingId !== conversation.id && (
                    <View style={styles.conversationActions}>
                      <IconButton
                        icon="play"
                        size={getScaledSize(20, uiScale)}
                        iconColor="#667eea"
                        onPress={() => selectConversation(conversation)}
                      />
                      <IconButton
                        icon="pencil"
                        size={getScaledSize(20, uiScale)}
                        iconColor="#ffd43b"
                        onPress={() => startEditing(conversation)}
                      />
                      <IconButton
                        icon="download"
                        size={getScaledSize(20, uiScale)}
                        iconColor="#51cf66"
                        onPress={() => exportConversation(conversation)}
                      />
                      <IconButton
                        icon="delete"
                        size={getScaledSize(20, uiScale)}
                        iconColor="#ff6b6b"
                        onPress={() => deleteConversation(conversation)}
                      />
                    </View>
                  )}
                </View>
                
                {index < conversations.length - 1 && <Divider style={styles.divider} />}
              </View>
            ))
          )}
        </Card.Content>
      </Card>

      {/* Información */}
      <Card style={styles.infoCard}>
        <Card.Content>
          <Text style={styles.infoText}>
            💡 Las conversaciones se guardan automáticamente. Puedes crear múltiples chats y cambiar entre ellos.
          </Text>
        </Card.Content>
      </Card>

    </ScrollView>
  );
}

const createStyles = (uiScale, textScale) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  content: {
    padding: getSpacing(16, uiScale),
    paddingBottom: getSpacing(32, uiScale),
  },
  card: {
    marginBottom: getSpacing(16, uiScale),
    backgroundColor: '#1a1a2e',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
  },
  cardTitle: {
    fontSize: getFontSize(16, textScale),
    color: '#ffffff',
    fontWeight: 'bold',
  },
  input: {
    marginBottom: getSpacing(16, uiScale),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    fontSize: getFontSize(16, textScale),
  },
  createButton: {
    backgroundColor: '#667eea',
  },
  buttonContent: {
    height: getScaledSize(40, uiScale),
  },
  emptyText: {
    fontSize: getFontSize(14, textScale),
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: getSpacing(20, uiScale),
  },
  conversationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: getSpacing(8, uiScale),
  },
  conversationInfo: {
    flex: 1,
    marginRight: getSpacing(8, uiScale),
  },
  conversationHeader: {
    marginBottom: getSpacing(4, uiScale),
  },
  conversationTitle: {
    fontSize: getFontSize(16, textScale),
    color: '#ffffff',
    fontWeight: '500',
    marginBottom: getSpacing(2, uiScale),
  },
  currentConversation: {
    color: '#51cf66',
  },
  characterName: {
    fontSize: getFontSize(12, textScale),
    color: '#667eea',
  },
  conversationDate: {
    fontSize: getFontSize(12, textScale),
    color: 'rgba(255, 255, 255, 0.6)',
  },
  conversationActions: {
    flexDirection: 'row',
  },
  editContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editInput: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    fontSize: getFontSize(14, textScale),
    marginRight: getSpacing(8, uiScale),
  },
  editButtons: {
    flexDirection: 'row',
  },
  divider: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginVertical: getSpacing(8, uiScale),
  },
  infoCard: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderColor: 'rgba(102, 126, 234, 0.3)',
    borderWidth: 1,
  },
  infoText: {
    fontSize: getFontSize(13, textScale),
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: getFontSize(18, textScale),
  },
});
