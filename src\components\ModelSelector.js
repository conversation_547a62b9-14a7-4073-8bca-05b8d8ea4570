import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Menu,
  ActivityIndicator,
} from 'react-native-paper';

import { getScaledSize, getFontSize, getSpacing } from '../theme/theme';
import { isVisionModel } from '../services/LMStudioService';

export default function ModelSelector({ models, currentModel, onModelChange, uiScale, textScale }) {
  const [menuVisible, setMenuVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleModelSelect = async (modelId) => {
    setMenuVisible(false);
    
    if (modelId === currentModel) {
      return; // Ya está seleccionado
    }

    Alert.alert(
      'Cambiar Modelo',
      `¿Quieres cargar el modelo "${modelId}"?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Cargar',
          onPress: async () => {
            setIsLoading(true);
            try {
              // TODO: Implementar carga de modelo
              // Por ahora solo actualizamos el estado
              onModelChange(modelId);
              
              // Simular tiempo de carga
              await new Promise(resolve => setTimeout(resolve, 1000));
              
            } catch (error) {
              Alert.alert('Error', `No se pudo cargar el modelo: ${error.message}`);
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleUnloadModel = () => {
    Alert.alert(
      'Descargar Modelo',
      '¿Estás seguro de que quieres descargar el modelo actual?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Descargar',
          style: 'destructive',
          onPress: () => {
            onModelChange(null);
          },
        },
      ]
    );
  };

  const styles = createStyles(uiScale, textScale);

  return (
    <Card style={styles.card}>
      <Card.Title title="🤖 Selector de Modelo" titleStyle={styles.cardTitle} />
      <Card.Content>
        
        {/* Modelo Actual */}
        <View style={styles.currentModelContainer}>
          <Text style={styles.currentModelLabel}>Modelo Actual:</Text>
          {currentModel ? (
            <View style={styles.modelInfo}>
              <View style={styles.modelNameContainer}>
                {isVisionModel(currentModel) && (
                  <Text style={styles.visionIcon}>👁️</Text>
                )}
                <Text style={styles.currentModelName} numberOfLines={1}>
                  {currentModel}
                </Text>
              </View>
              <Button
                mode="outlined"
                onPress={handleUnloadModel}
                style={styles.unloadButton}
                contentStyle={styles.buttonContent}
                textColor="#ff6b6b"
              >
                🗑️ Descargar
              </Button>
            </View>
          ) : (
            <Text style={styles.noModelText}>
              Ningún modelo cargado
            </Text>
          )}
        </View>

        {/* Selector de Modelos */}
        {models.length > 0 && (
          <View style={styles.selectorContainer}>
            <Menu
              visible={menuVisible}
              onDismiss={() => setMenuVisible(false)}
              anchor={
                <Button
                  mode="contained"
                  onPress={() => setMenuVisible(true)}
                  disabled={isLoading}
                  style={styles.selectButton}
                  contentStyle={styles.buttonContent}
                >
                  {isLoading ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    '📥 Seleccionar Modelo'
                  )}
                </Button>
              }
              contentStyle={styles.menuContent}
            >
              {models.map((model) => (
                <Menu.Item
                  key={model.id}
                  onPress={() => handleModelSelect(model.id)}
                  title={
                    <View style={styles.menuItemContainer}>
                      {isVisionModel(model.id) && (
                        <Text style={styles.menuVisionIcon}>👁️</Text>
                      )}
                      <Text style={styles.menuItemText} numberOfLines={1}>
                        {model.id}
                      </Text>
                      {model.id === currentModel && (
                        <Text style={styles.currentIndicator}>✅</Text>
                      )}
                    </View>
                  }
                  titleStyle={styles.menuItemTitle}
                />
              ))}
            </Menu>
          </View>
        )}

        {/* Información sobre modelos con visión */}
        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            💡 Los modelos con 👁️ soportan análisis de imágenes
          </Text>
        </View>

      </Card.Content>
    </Card>
  );
}

const createStyles = (uiScale, textScale) => StyleSheet.create({
  card: {
    marginBottom: getSpacing(16, uiScale),
    backgroundColor: '#1a1a2e',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
  },
  cardTitle: {
    fontSize: getFontSize(16, textScale),
    color: '#ffffff',
    fontWeight: 'bold',
  },
  currentModelContainer: {
    marginBottom: getSpacing(16, uiScale),
  },
  currentModelLabel: {
    fontSize: getFontSize(14, textScale),
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: getSpacing(8, uiScale),
  },
  modelInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modelNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: getSpacing(8, uiScale),
  },
  visionIcon: {
    fontSize: getFontSize(14, textScale),
    marginRight: getSpacing(6, uiScale),
  },
  currentModelName: {
    fontSize: getFontSize(16, textScale),
    color: '#51cf66',
    fontWeight: '500',
    flex: 1,
  },
  noModelText: {
    fontSize: getFontSize(16, textScale),
    color: 'rgba(255, 255, 255, 0.5)',
    fontStyle: 'italic',
  },
  selectorContainer: {
    marginBottom: getSpacing(16, uiScale),
  },
  selectButton: {
    backgroundColor: '#667eea',
  },
  unloadButton: {
    borderColor: '#ff6b6b',
  },
  buttonContent: {
    height: getScaledSize(40, uiScale),
  },
  menuContent: {
    backgroundColor: '#1a1a2e',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
    maxHeight: 300,
  },
  menuItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  menuVisionIcon: {
    fontSize: getFontSize(12, textScale),
    marginRight: getSpacing(6, uiScale),
  },
  menuItemText: {
    fontSize: getFontSize(14, textScale),
    color: '#ffffff',
    flex: 1,
  },
  currentIndicator: {
    fontSize: getFontSize(12, textScale),
    marginLeft: getSpacing(8, uiScale),
  },
  menuItemTitle: {
    fontSize: getFontSize(14, textScale),
    color: '#ffffff',
  },
  infoContainer: {
    padding: getSpacing(8, uiScale),
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderRadius: getScaledSize(6, uiScale),
    borderWidth: 1,
    borderColor: 'rgba(102, 126, 234, 0.3)',
  },
  infoText: {
    fontSize: getFontSize(12, textScale),
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
});
